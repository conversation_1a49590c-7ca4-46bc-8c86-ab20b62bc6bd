<template>
    <Head title="Contact" />

    <Header></Header>

    <section class="h-[200px] lg:h-[300px] flex flex-col items-center justify-center bg-gradient-to-r from-primaryColor to-black px-5 lg:px-0">
        <h1 class="text-2xl text-center md:text-3xl lg:text-5xl font-bold text-white">Contact Us</h1>
    </section>

    <section class="w-full my-32 px-6 lg:px-12 flex flex-col lg:flex-row space-y-6 lg:space-y-0 items-stretch justify-center lg:space-x-6">
        <div class="w-full lg:w-1/4 min-h-full p-8 border border-gray-300 rounded-lg">
            <div class="flex flex-col items-start">
                <div class="w-12 h-12 border border-gray-300 rounded-lg flex flex-col justify-center items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-5 text-gray-500">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M12 20.25c4.97 0 9-3.694 9-8.25s-4.03-8.25-9-8.25S3 7.444 3 12c0 2.104.859 4.023 2.273 5.48.432.447.74 1.04.586 1.641a4.483 4.483 0 0 1-.923 1.785A5.969 5.969 0 0 0 6 21c1.282 0 2.47-.402 3.445-1.087.81.22 1.668.337 2.555.337Z" />
                    </svg>
                </div>

                <div class="mt-12 flex flex-col items-start">
                    <h1 class="text-xl font-bold text-gray-600">Chat to Support</h1>
                    <p class="text-gray-500 mt-1">Speak to our support team</p>
                    <a href="mailto:<EMAIL>" class="mt-3 text-sm text-blue-600 hover:underline"><EMAIL></a>
                </div>
            </div>
        </div>

        <div class="w-full lg:w-1/4 min-h-full p-8 border border-gray-300 rounded-lg">
            <div class="flex flex-col items-start">
                <div class="w-12 h-12 border border-gray-300 rounded-lg flex flex-col justify-center items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-5 text-gray-500">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M12 20.25c4.97 0 9-3.694 9-8.25s-4.03-8.25-9-8.25S3 7.444 3 12c0 2.104.859 4.023 2.273 5.48.432.447.74 1.04.586 1.641a4.483 4.483 0 0 1-.923 1.785A5.969 5.969 0 0 0 6 21c1.282 0 2.47-.402 3.445-1.087.81.22 1.668.337 2.555.337Z" />
                    </svg>
                </div>

                <div class="mt-12 flex flex-col items-start">
                    <h1 class="text-xl font-bold text-gray-600">Chat to Sales</h1>
                    <p class="text-gray-500 mt-1">Speak to our support team</p>
                    <a href="mailto:<EMAIL>" class="mt-3 text-sm text-blue-600 hover:underline"><EMAIL></a>
                </div>
            </div>
        </div>

        <div class="w-full lg:w-1/4 min-h-full p-8 border border-gray-300 rounded-lg">
            <div class="flex flex-col items-start">
                <div class="w-12 h-12 border border-gray-300 rounded-lg flex flex-col justify-center items-center"> 
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-5 text-gray-500">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M12 20.25c4.97 0 9-3.694 9-8.25s-4.03-8.25-9-8.25S3 7.444 3 12c0 2.104.859 4.023 2.273 5.48.432.447.74 1.04.586 1.641a4.483 4.483 0 0 1-.923 1.785A5.969 5.969 0 0 0 6 21c1.282 0 2.47-.402 3.445-1.087.81.22 1.668.337 2.555.337Z" />
                    </svg>
                </div>

                <div class="mt-12 flex flex-col items-start">
                    <h1 class="text-xl font-bold text-gray-600">General Inquieries</h1>
                    <p class="text-gray-500 mt-1">General enquiries about Botomatic</p>
                    <a href="mailto:<EMAIL>" class="mt-3 text-sm text-blue-600 hover:underline"><EMAIL></a>
                </div>
            </div>
        </div>
 
    </section>

    <section class="w-full flex justify-center bg-primaryColor/20 mt-20 -mb-20">
        <div class="w-full px-6 lg:w-3/5 justify-center flex flex-col items-center pt-16  md:pt-20 lg:pt-24 pb-32 ">
            <h1 class="text-2xl lg:text-3xl text-gray-600 text-center">Start using Botomatic Now!</h1>

            <p class="mt-5 text-gray-500 text-md text-center">Set up in minutes. No credit card required.</p>

            <div class="mt-8 mb-10 flex flex-col space-y-2 lg:space-y-0 md:flex-row items-center md:space-x-2">
                <Link :href="route('register')" class="bg-primaryColor text-white text-sm md:text-md font-bold px-8 py-2 md:py-3 border-2 border-primaryColor hover:bg-transparent hover:text-primaryColor rounded-md">Register Now!</Link>

                <Link :href="route('login')" class="border-2 text-sm md:text-md border-primaryColor text-primaryColor hover:bg-primaryColor px-8 py-2 md:py-3 rounded-md hover:text-white">Login</Link>
            </div>
        </div>
    </section>  

    <Footer></Footer>

</template>
 

<script>
import { Head, Link } from '@inertiajs/vue3';
import Icon from '@/Components/Global/Icon.vue';
import Footer from '@/Components/Landing/Footer.vue';
import Header from '@/Components/Landing/Header.vue';

export default {
    components :{
        Head, Link,
        Icon,
        Footer,
        Header
    },

    data(){
        return { 
               
        }
    },
 
}

</script>