<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Widget Demo</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background-color: #f3f4f6;
            margin: 0;
            padding: 0;
        }

        .container {
            min-height: 100vh;
            padding: 20px;
        }

        h1 {
            font-size: 24px;
            font-weight: bold;
        }

        .chat-widget {
            width: 340px;
            height: 450px;
            background-color: #ffffff;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            border-radius: 16px;
            overflow: hidden;
            display: flex;
            flex-direction: column;
        }

        .chat-header {
            background-color: #4caf50;
            color: white;
            padding: 10px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-top-left-radius: 16px;
            border-top-right-radius: 16px;
        }

        .chat-header h1 {
            font-size: 16px;
            font-weight: bold;
        }

        .chat-header .profile {
            font-size: 12px;
            background-color: #ffffff;
            padding: 5px;
            border-radius: 20px;
            border: 1px solid white;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .chat-body {
            padding: 20px;
            flex-grow: 1;
            overflow-y: auto;
            display: flex;
            flex-direction: column;
        }

        .chat-body .message {
            margin-bottom: 15px;
        }

        .chat-body .message p {
            font-size: 12px;
            color: #333;
        }

        .chat-body .message.user p {
            text-align: right;
        }

        .chat-footer {
            display: flex;
            align-items: center;
            padding: 10px;
            border-top: 1px solid #ddd;
        }

        .chat-footer textarea {
            width: 85%;
            padding: 10px;
            border-radius: 8px;
            border: 1px solid #ddd;
            resize: none;
            font-size: 12px;
            color: #333;
        }

        .chat-footer button {
            width: 12%;
            padding: 10px;
            background-color: #4caf50;
            border: none;
            border-radius: 50%;
            color: white;
            font-size: 16px;
            cursor: pointer;
        }

        .chat-footer button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .dot-typing {
            width: 8px;
            height: 8px;
            background-color: gray;
            border-radius: 50%;
            animation: dot-blink 1s infinite step-end;
        }

        @keyframes dot-blink {
            0% {
                opacity: 0;
            }
            50% {
                opacity: 1;
            }
            100% {
                opacity: 0;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Widget Demo</h1>
        <div class="chat-widget">
            <div class="chat-header">
                <h1>Chatbot</h1>
                <div class="profile">
                    Profile
                </div>
            </div>

            <div class="chat-body" id="messagesContainer">
                <!-- Message Area -->
                <div class="message assistant">
                    <p>Hello! How can I help you today?</p>
                </div>

                <div class="message user">
                    <p>I'd like to know more about your services.</p>
                </div>

                <div class="message assistant">
                    <p>Sure! We provide various services...</p>
                </div>

                <div class="message typing">
                    <p class="dot-typing"></p>
                    <span>Typing...</span>
                </div>
            </div>

            <div class="chat-footer">
                <textarea id="messageInput" placeholder="Type a message..."></textarea>
                <button id="sendBtn" onclick="sendMessage()" disabled>Send</button>
            </div>
        </div>
    </div>

    <script>
        const sendButton = document.getElementById('sendBtn');
        const messageInput = document.getElementById('messageInput');
        const messagesContainer = document.getElementById('messagesContainer');

        messageInput.addEventListener('input', () => {
            sendButton.disabled = !messageInput.value.trim();
        });

        function sendMessage() {
            const userMessage = messageInput.value.trim();

            if (userMessage) {
                // Add user's message
                const userMessageElement = document.createElement('div');
                userMessageElement.classList.add('message', 'user');
                userMessageElement.innerHTML = `<p>${userMessage}</p>`;
                messagesContainer.appendChild(userMessageElement);

                // Simulate assistant response
                const assistantMessageElement = document.createElement('div');
                assistantMessageElement.classList.add('message', 'assistant');
                assistantMessageElement.innerHTML = `<p>I'm processing your request...</p>`;
                messagesContainer.appendChild(assistantMessageElement);

                // Scroll to the bottom of the message container
                messagesContainer.scrollTop = messagesContainer.scrollHeight;

                // Clear the input
                messageInput.value = '';
                sendButton.disabled = true;

                // Simulate typing animation
                setTimeout(() => {
                    assistantMessageElement.querySelector('p').innerText = "Here is the information you asked for!";
                    messagesContainer.scrollTop = messagesContainer.scrollHeight;
                }, 2000);
            }
        }
    </script>
</body>
</html>
