<?php

namespace App\Http\Controllers;

use App\Models\File;
use Illuminate\Http\Request;

class DocumentsController extends Controller
{
    public function store_user_file(Request $request)
    {

        $custom_validation_messages = [
            'max' => 'File size can not be morethan 5MB'
        ];

        // validate request
        $request->validate([
            'file' => ['file', 'mimes:jpeg,jpg,png,gif,webp,pdf,msword,docx,doc' ,'max:5000']
        ],$custom_validation_messages);

        // get file from request
        $file = request()->file('file');

        // store file details on the database
        $user_file = File::create([
            'file_name' => $file->getClientOriginalName(),
            'mime_type' => $file->getMimeType(),
            'file_size' => $file->getSize(),
            'user_id' => auth()->user()->id,  
        ]);

        // store file on disk
        $file_directory = "user_file/".auth()->user()->email."/{$user_file->created_at->format('Y/m/d')}/{$user_file->id}";

        $file->storeAs($file_directory, $user_file->file_name, 'public');        

        return [
            'id' => $user_file->id,
            'preview_url' => $user_file->preview_url,
        ];
    }
}
