<template> 
    <AppLayout title="Contacts">
        <div class="p-16"> 
            <div v-if="$page.props.contacts.length" class="w- border border-gray-100 rounded-lg px-8">
                <div class="py-3 mt-6 mb-10">
                    <div class="w-full flex items-center justify-between">
                        <h1 class="text-lg font-semibold text-gray-500">Captured Contacts</h1>

                    </div>
                </div>
                
                <DataTable  :value="$page.props.contacts" dataKey="id" tableStyle="min-width: 50rem; ">  
                    <Column field="created_at" header="Date" :sortable="true">
                        <template #body="slotProps">
                              {{moment(slotProps.data.created_at).fromNow()}}
                        </template>
                    </Column>

                    <Column field="name" header="Name" :sortable="true"></Column>
                    <Column field="email" header="Email"></Column>
                    <Column field="message" header="Message"></Column>
                    <Column header="Actions">
                        <template #body="slotProps">
                            <div class="w-full flex items-center space-x-2">
                                <div @click="showDetails(slotProps.data)" class="w-20 border border-gray-300 hover:bg-gray-100 cursor-pointer rounded-lg flex items-center text-xs py-2 text-gray-400 px-2"> 
                                    <icon name="info" class="w-3 mr-2"></icon> 
                                    Details
                                </div>

                                <button  @click="deleteContact(slotProps.data.id)" class="h-8 w-8 border border-gray-300 hover:bg-gray-100 cursor-pointer rounded-md flex flex-col items-center justify-center flex-shrink-0">
                                    <icon name="delete" class="w-5 text-red-300 "></icon> 
                                </button>
                            </div>
                        </template>
                    </Column>
                </DataTable>
 
            </div>

            <div v-else class="w-full border border-gray-200 rounded-lg py-10 flex justify-center flex-col items-center mt-16">
                No Contacts Saved Yet!
            </div>

        </div>
    </AppLayout>

    <Dialog v-model:visible="show_details" modal header="Stephen" :style="{ width: '30rem' }">
        <div class="w-full flex items-center space-x-2">
            <icon class="w-6 text-primaryColor" name="email"></icon>

            <span class="text-md  ">Email : <span class="text-gray-400">{{ selectedContact.email }}</span> </span>
        </div>
        <div class="w-full flex items-center space-x-2 mt-5">
            <icon class="w-5 text-primaryColor" name="date"></icon>
            <span class="text-md ">Date : <span class="text-gray-400">{{moment(selectedContact.created_at).fromNow()}}</span> </span>
        </div>
        
        <div class="mt-6 w-full border border-gray-300 rounded-lg">
            <div class="w-full py-2 px-4 border-b border-gray-300">
                <span class="text-sm text-gray-400">Questions & Answers</span>
            </div>

            <div class="w-full py-2 px-4 flex items-center space-x-2">
                <span class="text-sm font-bold">Question Status : </span>
                <span class="text-sm ">Contact Answered</span>
            </div>
        </div>
    </Dialog>

    <ConfirmDialog></ConfirmDialog>

</template>

<script> 
import AppLayout from '@/Layouts/AppLayout.vue'; 
import Icon from '@/Components/Global/Icon.vue'    
import {  Link } from '@inertiajs/vue3'; 
import DataTable from 'primevue/datatable';
import Column from 'primevue/column';    
import moment from 'moment';
import Dialog from 'primevue/dialog';
import ConfirmDialog from 'primevue/confirmdialog'; 
import 'primeicons/primeicons.css' 

export default {
    components :{ 
        AppLayout,
        Icon,
        Link, 
        DataTable,
        Column,
        Dialog,
        ConfirmDialog, 
    },

    props : {
        currently_selected_chatbot : Object
    },

    data() {
        return {   
            moment: moment,  
            show_details : false,
            selectedContact : null,

        }
    },

    methods: { 
        toggle(event) {
            this.$refs.op.toggle(event);
        },

        showDetails(data){
            this.show_details = true ;
            this.selectedContact = data ;
        },

        deleteContact(contact_id) {
            this.$confirm.require({
                message: 'Do you want to delete this contact?',
                header: 'Delete Contact',
                icon: 'pi pi-info-circle',
                rejectProps: {
                    label: 'Cancel',
                    severity: 'secondary',
                    outlined: true
                },
                acceptProps: {
                    label: 'Delete',
                    severity: 'danger'
                },

                accept: () => {
                    this.$inertia.delete(route('delete_contact', contact_id) , { 
                        preserveState: false, 
                    });
                },
                reject: () => {
                    
                }
            });
        }
   
        },
}
</script>