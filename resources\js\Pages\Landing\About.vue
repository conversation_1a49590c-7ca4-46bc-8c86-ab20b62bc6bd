<template>
    <Head title="About" />

    <Header></Header>

 
    <section class="h-[200px] lg:h-[300px] flex flex-col items-center justify-center bg-gradient-to-r from-primaryColor to-black">
        <h1 class="text-2xl md:text-3xl lg:text-5xl font-bold text-white">About Us</h1>
    </section>

    <section class="w-full ">
        <div class="mt-16 md:mt-24 lg:mt-28 lg:px-36 px-5">
            <h1 class="text-xl text-gray-600 tracking-wide">ABOUT</h1>

            
            <p class="mt-6 text-md text-gray-600">
                At Botomatic, we believe that every business deserves an intelligent, responsive, and reliable AI assistant. That’s why we created a simple, powerful platform that turns your company data into a fully functional chatbot in minutes.
            </p>    
            
            <p class="mt-6 text-md text-gray-600">
                Our platform makes it easy for businesses to upload their documents, FAQs, and resources, and instantly generate an AI-powered bot that can answer questions, assist customers, and streamline support — on your website, app, or anywhere you need it.
            </p>
        </div>
        
        <div class="mt-20 mb-28 px-5 lg:px-36 flex flex-col lg:flex-row items-center lg:items-start lg:space-x-12">
            <div class="w-full lg:w-1/2">
                <h1 class="text-xl text-gray-600 tracking-wide">OUR MISSION</h1>

                <p class="mt-6 text-md text-gray-600 tracking-wide">
                    We aim to empower businesses of all sizes with cutting-edge AI technology, eliminating the need for complex coding or costly development. By simplifying bot creation, we help companies enhance customer experiences, reduce manual workloads, and scale without limits.
                </p>

                <p class="mt-6 text-md text-gray-600 tracking-wide">
                    Our mission is to make AI an accessible tool for every business. We’re committed to innovation, constantly refining our platform so that your bots become smarter, faster, and more capable — evolving alongside your business needs.
                </p>
            </div>

            <div class="w-full lg:w-1/2 mt-12 lg:mt-0">
                <h1 class="text-xl text-gray-600 tracking-wide">OUR VISION</h1>

                <p class="mt-6 text-md text-gray-600 tracking-wide">
                    We envision a future where businesses can harness the power of AI without barriers, transforming the way they interact with customers and unlocking new possibilities for innovation and growth.
                </p>

                <p class="mt-6 text-md text-gray-600 tracking-wide">
                    Whether you're a startup looking for 24/7 customer support or a corporation wanting to automate internal processes, Botomatic is here to make AI simple, effective, and accessible.
                </p>
            </div>
        </div>  
    </section>

    <section class="w-full flex justify-center bg-primaryColor/20 mt-20 -mb-20">
        <div class="w-full px-6 lg:w-3/5 justify-center flex flex-col items-center pt-16  md:pt-20 lg:pt-24 pb-32 ">
            <h1 class="text-2xl lg:text-3xl text-gray-600 text-center">Start using Botomatic Now!</h1>

            <p class="mt-5 text-gray-500 text-md text-center">Set up in minutes. No credit card required.</p>

            <div class="mt-8 mb-10 flex flex-col space-y-2 lg:space-y-0 md:flex-row items-center md:space-x-2">
                <Link :href="route('register')" class="bg-primaryColor text-white text-sm md:text-md font-bold px-8 py-2 md:py-3 border-2 border-primaryColor hover:bg-transparent hover:text-primaryColor rounded-md">Register Now!</Link>

                <Link :href="route('login')" class="border-2 text-sm md:text-md border-primaryColor text-primaryColor hover:bg-primaryColor px-8 py-2 md:py-3 rounded-md hover:text-white">Login</Link>
            </div>
        </div>
     </section>
    <Footer></Footer>

</template>
 

<script>
import { Head, Link } from '@inertiajs/vue3';
import Icon from '@/Components/Global/Icon.vue';
import Footer from '@/Components/Landing/Footer.vue';
import Header from '@/Components/Landing/Header.vue';

export default {
    components :{
        Head, Link,
        Icon,
        Footer,
        Header
    },

    data(){
        return { 
            faqs1: [
                {
                    open: true,
                    question: 'What is Skulbot?',
                    answer: 'Skulbot is an AI-powered platform designed to help students and teachers streamline learning and teaching with automation and smart tools.',
                },
                {
                    open: false,
                    question: 'How does Skulbot help teachers?',
                    answer: 'Skulbot generates lesson plans, quizzes, and summaries, saving teachers hours of preparation time while improving student engagement.',
                },
                {
                    open: false,
                    question: 'Can students use Skulbot for exam preparation?',
                    answer: 'Yes! Skulbot provides AI-generated quizzes, study summaries, and personalized learning recommendations to help students prepare effectively.',
                },
                {
                    open: false,
                    question: 'Is Skulbot free to use?',
                    answer: 'Skulbot offers a free plan with essential features, and premium plans with advanced AI tools for enhanced learning and teaching experiences.',
                },
                {
                    open: false,
                    question: 'Does Skulbot support multiple subjects?',
                    answer: 'Yes, Skulbot works across various subjects, helping students and teachers in different fields with AI-powered content generation and assistance.',
                },
            ],

            faqs2: [
                {
                    open: false,
                    question: 'How do I sign up for Skulbot?',
                    answer: 'Simply visit our website, click on "Sign Up," and follow the easy steps to create your account.',
                },
                {
                    open: false,
                    question: 'Can I use Skulbot on my phone?',
                    answer: 'Yes! Skulbot is accessible on both desktop and mobile devices, making it easy to study and teach on the go.',
                },
                {
                    open: false,
                    question: 'Does Skulbot work with Google Classroom?',
                    answer: 'Yes, Skulbot integrates with Google Classroom and other learning management systems to enhance your teaching experience.',
                },
                {
                    open: false,
                    question: 'Is my data safe with Skulbot?',
                    answer: 'Absolutely! Skulbot follows strict security protocols to keep your data safe and private.',
                },
                {
                    open: false,
                    question: 'How do I contact support?',
                    answer: 'You can reach our support team through the "Help" section on our website or email us for assistance.',
                },
            ],
             
        }
    },
 
}

</script>