<template>
    <div id="spinner"></div>
</template>

<style scoped>
    * {
  padding: 0;
  margin: 0;
}

@keyframes spinner {
  from {
    transform: rotate(0deg);
  } to {
    transform: rotate(360deg);
  }
}
 

#spinner {
  min-width: 100px;
  min-height: 100px;
  border: 8px solid rgba(53,144,209,.1);
  border-right: 10px solid gray;
  border-radius: 50%;
  animation: spinner 1s linear infinite;
}
</style>