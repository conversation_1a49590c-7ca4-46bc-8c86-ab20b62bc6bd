<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('text_vectors', function (Blueprint $table) {
            $table->unsignedBigInteger('training_materials_id')->nullable();

            $table->foreign('training_materials_id')->references('id')->on('training_materials')->onDelete('cascade');

        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('text_vectors', function (Blueprint $table) {
            $table->dropColumn('training_materials_id');
        });
    }
};
