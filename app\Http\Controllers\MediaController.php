<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Models\Media;
use Illuminate\Http\Request; 
use Illuminate\Support\Facades\Storage;

class MediaController extends Controller
{
     // store the file
     public function store(Request $request)
     { 
             //Get the file from the request        
             $file = $request->file('file');
 
             //Store the file into the storage Folder
             $file->store('media/' . $request->user()->id . '/' . now()->format('Y') . '/' . now()->format('m') , 'public');
     
             //Create the record in the DB
             $media = Media::create([
                 'file_name' => $file->hashName(),
                 'mime_type' => $file->getMimeType(),
                 'file_size' => $file->getSize(),
                 'user_id' => auth()->user()->id, 
                 'model_type' => User::class ,
                 'model_id' => auth()->user()->id, 
             ]);
     
             //Return Json Response with Media Id
             return response()->json([
                 'id' => $media->id
             ]);
 
     }


     public function destroy(Media $media)
     {
         //Delete the file from storage
         Storage::disk('public')
         ->delete('media/' . $media->user_id . '/' . now()->format('Y') . '/' . now()->format('m') . '/' . $media->filename);
 
         //Delete record from DB
         $media->delete();
 
         //Return Json Response
         return response()->json([
             'message' => 'media deleted'
         ]);
 
     }
}
