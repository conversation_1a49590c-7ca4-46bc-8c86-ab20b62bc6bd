<template>
    <div>
        <Head :title="title" />

        <Banner />
 
        <div class="min-h-screen bg-gray-100">

            <div class="h-screen w-full flex items-stretch overflow-hidden">

                <div class="hidden lg:flex w-1/5 " >
                    <LeftSideBar/> 
                </div>

                <div class="flex-1 flex flex-col lg:rounded-tl-3xl shadow-md lg:mt-5">
                    <nav class="w-full bg-white lg:rounded-tl-3xl border-b border-t border-t-gray-100 border-gray-200 py-2">
                        <!-- Primary Navigation Menu -->
                        <div class="max-w-7xl mx-auto ">
                            <div class="flex items-center justify-between h-12 lg:h-16 px-2 lg:px-4">
                                <div v-if="isRoute('dashboard') || isRoute('profile.show') " class="ml-5">
                                    <div class="font-bold text-base text-gray-800">
                                        {{ $page.props.auth.user.name }}
                                    </div>
                                    <div class="font-sm text-sm text-gray-500">
                                        Welcome back to Kimana &#x1F44B;
                                    </div>
                                </div>

                                <div v-else class="px-3 flex items-center text-gray-400">
                                   
                                    {{ title }}
                                </div>

                                <div class="lg:flex hidden items-center ms-4">
                                    <Dropdown align="right" width="48">
                                    <template #trigger>
                                        <button v-if="$page.props.jetstream.managesProfilePhotos" class="flex text-sm border-2 border-transparent rounded-full focus:outline-none focus:border-gray-300 transition">
                                            <img class="h-10 w-10 rounded-full object-cover" :src="$page.props.auth.user.profile_photo_url" :alt="$page.props.auth.user.name">
                                        </button>

                                        <span v-else class="inline-flex rounded-md">
                                            <button type="button" class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-gray-500 bg-white hover:text-gray-700 focus:outline-none focus:bg-gray-50 active:bg-gray-50 transition ease-in-out duration-150">
                                                {{ $page.props.auth.user.name }}

                                                <svg class="ms-2 -me-0.5 h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" d="M19.5 8.25l-7.5 7.5-7.5-7.5" />
                                                </svg>
                                            </button>
                                        </span>
                                    </template>

                                    <template #content>
                                        <!-- Account Management -->
                                        <div class="flex items-center border-b border-gray-200 py-3 px-2 space-x-3">
                                            <img class="h-10 w-10 rounded-full object-cover" :src="$page.props.auth.user.profile_photo_url" :alt="$page.props.auth.user.name">
                                            <h1 class="text-sm font-bold">  {{ $page.props.auth.user.name }}</h1>
                                        </div>

                                        <DropdownLink :href="route('profile.show')" >
                                            Your Profile
                                        </DropdownLink> 

                                        <div class="border-t border-gray-200" />

                                        <!-- Authentication -->
                                        <form @submit.prevent="logout">
                                            <DropdownLink as="button">
                                                Log Out
                                            </DropdownLink>
                                        </form>
                                    </template>
                                </Dropdown>

                                </div>
                                    
                                <div class="lg:hidden">
                                    <ResponsiveMenu/>
                                </div>

                            </div>
                        </div>

                    </nav>

                    <!-- Page Content -->
                    <main class="w-full h-full overflow-y-auto bg-white ">
                        <slot />
                    </main>
                </div>
            </div>
        </div>
    </div>
</template>

<script>  
import { Head, Link } from '@inertiajs/vue3';
import LeftSideBar from '@/Components/Global/LeftSideBar.vue'; 
import Banner from '@/Components/Banner.vue';
import Dropdown from '@/Components/Dropdown.vue';
import DropdownLink from '@/Components/DropdownLink.vue'; 
import Icon from '@/Components/Global/Icon.vue'     
import ResponsiveMenu from '@/Components/Global/ResponsiveMenu.vue';

export default {
    components :{  
        Icon,
        Head, Link,
        LeftSideBar,
        Banner,
        Dropdown,
        DropdownLink,
        ResponsiveMenu
    },

    props : {
        title: String,
    },

    data() {
        return {  
            titles : [
                        {
                            'icon' : 'home',
                            'link' : 'dashboard', 
                        }, 

                        {
                            'icon' : 'appearance',
                            'link' : 'appearance', 
                        },  
                            
                        {
                            'icon' : 'try_chat',
                            'link' : 'index', 
                        },  

                                
                        {
                            'icon' : 'training_material',
                            'link' : 'index', 
                        }, 

                        {
                            'icon' : 'questions_answers',
                            'link' : 'index', 
                        },   

                        {
                            'icon' : 'business_check',
                            'link' : 'index', 
                        },   
                    ],
            current_page_title : null
        }
    },

    methods: { 
        logout() {
                this.$inertia.post(route('logout'));
            }, 
        },

    computed : {
        pageIcon(){
            this.titles.forEach(title => {
                if (this.isRoute(title.link)) {
                    this.current_page_title = title; 
                }
            });

            return this.current_page_title.icon;
        }
    }
}
</script>