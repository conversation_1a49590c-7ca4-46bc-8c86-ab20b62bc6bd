<?php

namespace App\Http\Middleware;

use Closure;
use Inertia\Inertia;
use Illuminate\Http\Request;
use App\Models\OnboardingField;
use Symfony\Component\HttpFoundation\Response;
use App\Models\Chatbot;

class Onboarding
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next)
    {
        if (auth()->user()->onboarding_done == false) { 

            $user_onboarding_data = OnboardingField::where('user_id', auth()->user()->id)->first();

            if (!$user_onboarding_data) { 
                $user_onboarding_data = OnboardingField::create([
                    'user_id' => auth()->user()->id
                ]);
            }
            return Inertia::render('Onboarding/Onboarding',[ 
                 'user_onboarding_data' => $user_onboarding_data
            ]); 

        } else {
            return $next($request);         
        }
    }
}
