<?php

namespace App\Http\Controllers;

use Inertia\Inertia;
use App\Models\TextData;
use App\Models\TextVector;
use Illuminate\Http\Request;
use App\Models\TrainingMaterial;
use Illuminate\Http\UploadedFile;
use OpenAI\Laravel\Facades\OpenAI;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Http;
use Sastrawi\Stemmer\StemmerFactory;
use App\Services\TextProcessingService;
use Illuminate\Support\Facades\Storage;

class TrainingMaterialsController extends Controller
{

    private $apiKey;
    private $baseUrl;

    public function __construct()
    {
        $this->apiKey = env('PINECONE_API_KEY');
        $this->baseUrl = 'https://api.pinecone.io/assistant';
    }
     
    public function training_materials() {
 
        $training_materials = TrainingMaterial::where('user_id', auth()->user()->id)->get();

        $chatbot = $this->selectedBot(); 
        
        if ($chatbot->training_status == false) { 
            $pdf_path = 'trainingfiles/' . auth()->user()->id .'/'. $chatbot->id .'/pdfs';
        
            if (Storage::disk('public')->exists($pdf_path)) {
                $files = Storage::disk('public')->files($pdf_path);

                if (count($files) > 0){

                    $assistant_name = 'assistant' . auth()->user()->id . $chatbot->id ;

                    if (!empty($files)) {
                        foreach ($files as $file) {
                            $filePath = Storage::disk('public')->path($file);
                            $uploadedFile = new UploadedFile(
                                $filePath,
                                basename($file),
                                mime_content_type($filePath),
                                null,
                                true
                            );

                            $uploadedFiles[] = $uploadedFile;
                        }
                    }

                    $this->uploadFilesToAssistant($assistant_name, $uploadedFiles);
                }
            }   

            $txt_path = 'trainingfiles/' . auth()->user()->id .'/'. $chatbot->id .'/linktxtfiles';

            
            if (Storage::disk('public')->exists($txt_path)) {
                $files = Storage::disk('public')->files($txt_path);

                if (count($files) > 0){

                    $assistant_name = 'assistant' . auth()->user()->id . $chatbot->id ;

                    if (!empty($files)) {
                        foreach ($files as $file) {
                            $filePath = Storage::disk('public')->path($file);
                            $uploadedFile = new UploadedFile(
                                $filePath,
                                basename($file),
                                mime_content_type($filePath),
                                null,
                                true
                            );

                            $uploadedFiles[] = $uploadedFile;
                        }
                    }

                    $this->uploadFilesToAssistant($assistant_name, $uploadedFiles);
                }
            } 

        } 
        
        return Inertia::render('TrainingMaterials/TrainingMaterials', [ 
            'chatbots' => $this->chatbots(),
            'currently_selected_chatbot' => $this->selectedBot(),
            'training_materials' => $training_materials,
        ]);
    }

    public function delete_training_material(Request $request) {
        
        $userId = auth()->user()->id;
        $chatbot = $this->selectedBot();
        $chatbotId = $chatbot->id;

        $assistant_name = 'assistant' . $userId . $chatbotId ;

         // Fetch the assistant's files
         $filesResponse = Http::withHeaders([
            'Api-Key' => $this->apiKey,
            'X-Pinecone-API-Version' => '2025-01',
        ])->get("https://prod-1-data.ke.pinecone.io/assistant/files/{$assistant_name}");

        $files = $filesResponse->json(); 

        if (isset($files) && is_array($files)) {

            foreach ($files['files'] as $file) { 

                foreach ($request->selectedMaterial as $material) {

                    if ($material['material_type'] == 'File') {

                        if ($material['material_title'] == $file['name']) { 
                            $fileId = $file['id'];
    
                            $url = "https://prod-1-data.ke.pinecone.io/assistant/files/{$assistant_name}/{$fileId}";
                    
                            $response = Http::withHeaders([
                                'Api-Key' => $this->apiKey,
                            ])->delete($url);
                    
                            if (!$response->successful()) { 
    
                                return [
                                    'error' => true,
                                    'message' => $response->body(),
                                    'status' => $response->status(),
                                ];
                            }  
                        }

                    }else {
                        $material_name = $material['random_id'].'.txt';

                        if ($material_name == $file['name']) { 
                            $fileId = $file['id'];
    
                            $url = "https://prod-1-data.ke.pinecone.io/assistant/files/{$assistant_name}/{$fileId}";
                    
                            $response = Http::withHeaders([
                                'Api-Key' => $this->apiKey,
                            ])->delete($url);
                    
                            if (!$response->successful()) { 
    
                                return [
                                    'error' => true,
                                    'message' => $response->body(),
                                    'status' => $response->status(),
                                ];
                            }  
                        }
                    }
                    
                }
            }

        }

        foreach ($request->selectedMaterial as $file) {
            if ($file['material_type'] == 'File') {
                $filePath = 'trainingfiles/' . $userId . '/' . $chatbotId . '/pdfs/' .$file['material_title']; 
                
            }else {
                $filePath = 'trainingfiles/' . $userId . '/' . $chatbotId . '/linktxtfiles/' . $file['random_id'] . '.txt';
            }

            // Delete the file from storage
            if (Storage::disk('public')->exists($filePath)) {
                Storage::disk('public')->delete($filePath);
            }
        } 

        $ids = collect($request->selectedMaterial)->pluck('id');
        TrainingMaterial::whereIn('id', $ids)->delete();
        
        return redirect()->back();
    }

    //upload files to assistant
    private function uploadFilesToAssistant ($assistant_name , $files) {

        foreach ($files as $file) {
            $metadata = json_encode([
                'uploaded_at' => now()->toDateTimeString(),
                'file_name' => $file->getClientOriginalName(),
            ]);

            $response = Http::withHeaders([
                'Api-Key' => $this->apiKey,
            ])->attach(
                'file',
                file_get_contents($file->getRealPath()),
                $file->getClientOriginalName()
            )->post("https://prod-1-data.ke.pinecone.io/assistant/files/{$assistant_name}?metadata=" . urlencode($metadata));

            if (!$response->successful()) {
                return response()->json(['error' => 'Failed to upload file to assistant', 'details' => $response->json()], 500);
            }else {

                $chatbot = $this->selectedBot();

                $chatbot->forceFill([
                    'training_status' => true,
                ])->save();
            }
        }

    }
    
 
}
