<?php

namespace App\Http\Controllers;

use App\Models\User;
use Inertia\Inertia;
use App\Models\Chatbot;
use App\Models\Business;
use Illuminate\Http\Request;
use App\Models\OnboardingField;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Storage;

class OnboardingController extends Controller
{

    public function change_onboarding_step(Request $request , $step ,$operation) { 

        $user = User::where('id', auth()->user()->id)->first();

        if ($operation == 'minus') {
            $new_step = $step - 1;

            $user->forceFill([
                'onboarding_step' => $new_step
            ])->save();
        } 

        $user_onboarding_data = OnboardingField::where('user_id', auth()->user()->id)->first();

        if ($step == 1 ) {

            $user_onboarding_data->forceFill([
                'user_id' => auth()->user()->id,
                'company_name' => $request->company_name,
                'industry' => $request->industry,
                'company_website' => $request->company_website,
                'company_size' => $request->company_size,
                'audience' => $request->audience,
            ])->save();

            $newbot = Chatbot::create([
                'user_id' => auth()->user()->id,
                'random_id' => $this->randomNumber(),  
            ]);

            $user = User::where('id', auth()->user()->id)->first();

            $user->forceFill([
                'currently_selected_chatbot' => $newbot->id
            ])->save();

        } 


        if ($operation == 'plus') {
            $new_step = $step + 1;
        } 

        if ($new_step < 5 ) { 
            $user->forceFill([
                'onboarding_step' => $new_step
            ])->save();

            return Inertia::location(url()->previous());

        }else {

            $user->forceFill([
                'onboarding_step' => $new_step,
                'onboarding_done' => true,
            ])->save();

            return redirect()->route('dashboard');
        }

        
    }

}
