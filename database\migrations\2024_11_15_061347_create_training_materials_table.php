<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('training_materials', function (Blueprint $table) {
            $table->id(); 
            $table->unsignedBigInteger('user_id');
            $table->unsignedBigInteger('chatbot_id');
            $table->string('random_id')->nullable();
            $table->string('material_title')->nullable();
            $table->string('material_type')->nullable();
            $table->string('charecter_count')->nullable();
            $table->boolean('trained')->default(false);
            $table->timestamp('last_trained')->nullable();
            $table->longText('data')->nullable();
            $table->timestamps();

            $table->foreign('chatbot_id')->references('id')->on('chatbots')->onDelete('cascade');

        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('training_materials');
    }
};
