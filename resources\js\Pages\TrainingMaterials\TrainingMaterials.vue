<template>
    <AppLayout title="Training Materials">
        <div class="p-5 lg:p-16"> 

            <div class="w-full border border-gray-200 rounded-md py-10 px-6 lg:px-12 overflow-y-auto overflow-x-none">
                    <h1 class="text-md md:text-lg lg:text-xl font-bold">Add New Training Data</h1>

                    <div v-if="form.data_transfer_type == null"class="mt-10 flex  flex-col lg:flex-row space-y-5 lg:space-y-0 items-center lg:space-x-5">
                        <button @click="selectDataTransferType(1)" class="w-full lg:w-1/2 rounded-2xl border border-gray-300 hover:bg-gray-50 border-dashed py-10 flex flex-col items-center justify-center">
                            <div class="w-full flex flex-col items-center">
                                <div class="w-14 h-14 rounded-xl bg-black flex flex-col items-center justify-center">
                                    <icon name="globe" class="w-5 text-white "></icon>
                                </div>

                                <span class="text-sm text-gray-500 mt-2">Website</span>
                            </div>
                        </button>

                        <button @click="selectDataTransferType(2)" class="w-full lg:w-1/2 rounded-2xl border border-gray-300 hover:bg-gray-50 border-dashed py-10 flex flex-col items-center justify-center">
                            <div class="w-full flex flex-col items-center">
                                <div class="w-14 h-14 rounded-xl bg-black flex flex-col items-center justify-center">
                                    <icon name="documents" class="w-5 text-white "></icon>
                                </div>

                                <span class="text-sm text-gray-500 mt-2">Document</span>
                            </div>
                        </button>

                    </div>

                    <div v-if="form.data_transfer_type == 1" class="mt-6 w-full flex flex-col items-start">
                        <span class="text-sm">Your website</span>

                        <div class="w-full flex items-stretch space-x-2">
                            <TextInput
                                id="email"
                                v-model="form.website"
                                type="text"
                                class="mt-1 block w-[70%] md:w-[80%] min-h-full"
                                required
                                autofocus
                                autocomplete="company_website"
                            />

                            <button @click="getUrlLinks" class="py-1 w-[30%] md:w-[20%] hover:opacity-90 min-h-full rounded-lg bg-primaryColor text-white text-xs md:text-sm">Fetch Links</button>

                        </div>

                        
                        <div v-if="loading" class="w-full flex justify-center mt-16" >
                            <LargeSpinner/>
                        </div>

                        <div v-if="links_error == true" class="w-full flex justify-center">
                            <div class="mt-10 border border-dashed border-red-300 py-10 w-3/5 flex justify-center items-center flex-col">
                                <p class="text-sm text-red-600">Error while fetching links.</p>
                            </div>
                        </div>

                        <div class="w-full mt-8 px-2 overflow-clip py-5" v-if="form.fetchedLinks.length > 0">

                            <p class="text-sm text-gray-500 mb-5">We found the following links. You can delete links you don't want, or add more to give more knowledge to your bot!</p>

                            <ul class="w-full mt-5">
                                <li class="w-[95%] " v-for="(link, index) in form.fetchedLinks" :key="index"> 
                                    <div class="w-full py-2 flex items-center justify-center">
                                        <div class="w-[93%] py-2 "> 
                                            <span class="text-sm break-words text-gray-500">{{ link }}</span>
                                        </div> 

                                        <button @click="removeLink(index)" class="w-[7%] h-9 hover:bg-gray-100 cursor-pointer rounded-full flex justify-center flex-col items-center">
                                            <icon name="x" class="w-5 text-red-500"></icon>
                                        </button>
                                    </div>
                                </li>
                            </ul>

                            <div v-if="addMoreLinks == true"class="mt-10 flex items-center space-x-2">
                                <TextInput
                                        id="link"
                                        v-model="new_link"
                                        type="text"
                                        class="mt-1 block w-full" 
                                        autofocus 
                                    />

                                <button @click="addNewLinkToList" class="w-20 bg-gray-400 text-white text-center py-2 rounded-sm">Add</button>
                            </div>

                            <div class="w-full flex justify-start mt-5">
                                <button @click="addMoreLinks = !addMoreLinks" v-if="addMoreLinks == false" class="border-2 border-primaryColor px-4 py-1 text-xs text-primaryColor rounded-lg">Add More</button>
                            </div>

                            <div class="w-full flex justify-end mt-5">
                                <button @click="submitTrainingLinks()" v-if="addMoreLinks == false" class="border-2 border-primaryColor px-6 lg:px-8 py-1 lg:py-2 text-sm md:text-md bg-primaryColor rounded-lg text-white">Save</button>
                            </div>
                        </div>
 
                    </div>

                    <div v-if="form.data_transfer_type == 2" class="w-full">
                       
                        <div class="card mt-10">
                            <Toast />

                            <FileUpload name="demo[]" url="/api/upload" @upload="onTemplatedUpload($event)" :multiple="true" accept=".pdf" :maxFileSize="5000000" @select="onSelectedFiles">
                                <template #header="{ chooseCallback, uploadCallback, clearCallback, files }">
                                    <div class="flex flex-wrap justify-between items-center flex-1 gap-4">
                                        <span class="text-sm md:text-md">
                                            Upload PDF(s)
                                        </span>

                                        <div class="flex gap-2">
                                            <Button @click="chooseCallback()" icon="pi pi-images" rounded outlined severity="secondary">
                                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-file-earmark-pdf" viewBox="0 0 16 16">
                                                <path d="M14 14V4.5L9.5 0H4a2 2 0 0 0-2 2v12a2 2 0 0 0 2 2h8a2 2 0 0 0 2-2M9.5 3A1.5 1.5 0 0 0 11 4.5h2V14a1 1 0 0 1-1 1H4a1 1 0 0 1-1-1V2a1 1 0 0 1 1-1h5.5z"/>
                                                <path d="M4.603 14.087a.8.8 0 0 1-.438-.42c-.195-.388-.13-.776.08-1.102.198-.307.526-.568.897-.787a7.7 7.7 0 0 1 1.482-.645 20 20 0 0 0 1.062-2.227 7.3 7.3 0 0 1-.43-1.295c-.086-.4-.119-.796-.046-1.136.075-.354.274-.672.65-.823.192-.077.4-.12.602-.077a.7.7 0 0 1 .477.365c.088.164.12.356.127.538.007.188-.012.396-.047.614-.084.51-.27 1.134-.52 1.794a11 11 0 0 0 .98 1.686 5.8 5.8 0 0 1 1.334.05c.364.066.734.195.96.465.12.144.193.32.2.518.007.192-.047.382-.138.563a1.04 1.04 0 0 1-.354.416.86.86 0 0 1-.51.138c-.331-.014-.654-.196-.933-.417a5.7 5.7 0 0 1-.911-.95 11.7 11.7 0 0 0-1.997.406 11.3 11.3 0 0 1-1.02 1.51c-.292.35-.609.656-.927.787a.8.8 0 0 1-.58.029m1.379-1.901q-.25.115-.459.238c-.328.194-.541.383-.647.547-.094.145-.096.25-.04.361q.016.032.026.044l.035-.012c.137-.056.355-.235.635-.572a8 8 0 0 0 .45-.606m1.64-1.33a13 13 0 0 1 1.01-.193 12 12 0 0 1-.51-.858 21 21 0 0 1-.5 1.05zm2.446.45q.226.245.435.41c.24.19.407.253.498.256a.1.1 0 0 0 .07-.015.3.3 0 0 0 .094-.125.44.44 0 0 0 .059-.2.1.1 0 0 0-.026-.063c-.052-.062-.2-.152-.518-.209a4 4 0 0 0-.612-.053zM8.078 7.8a7 7 0 0 0 .2-.828q.046-.282.038-.465a.6.6 0 0 0-.032-.198.5.5 0 0 0-.145.04c-.087.035-.158.106-.196.283-.04.192-.03.469.046.822q.036.167.09.346z"/>
                                                </svg>
                                            </Button>

                                            <Button @click="clearCallback()" icon="pi pi-times" rounded outlined severity="danger" :disabled="!files || files.length === 0"></Button>
                                        </div>

                                        <ProgressBar :value="totalSizePercent" :showValue="false" class="md:w-20rem h-1 w-full md:ml-auto">
                                            <span class="whitespace-nowrap">{{ totalSize }}B / 1Mb</span>
                                        </ProgressBar>
                                    </div>
                                </template>

                                <template #content="{ files, uploadedFiles, removeUploadedFileCallback, removeFileCallback, messages }">
                                    <div class="flex flex-col gap-8 pt-4">
                                        <Message v-for="message of messages" :key="message" :class="{ 'mb-8': !files.length && !uploadedFiles.length}" severity="error">
                                            {{ message }}
                                        </Message>

                                        <div v-if="files.length > 0">
                                            <div class="flex flex-wrap gap-4">
                                                <div v-for="(file, index) of files" :key="file.name + file.type + file.size" class="p-8 rounded-border flex flex-col border border-surface items-center gap-4">
                                                    <div> 
                                                        <div v-if="file.type == 'application/pdf'" class="">
                                                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-filetype-pdf" viewBox="0 0 16 16">
                                                            <path fill-rule="evenodd" d="M14 4.5V14a2 2 0 0 1-2 2h-1v-1h1a1 1 0 0 0 1-1V4.5h-2A1.5 1.5 0 0 1 9.5 3V1H4a1 1 0 0 0-1 1v9H2V2a2 2 0 0 1 2-2h5.5zM1.6 11.85H0v3.999h.791v-1.342h.803q.43 0 .732-.173.305-.175.463-.474a1.4 1.4 0 0 0 .161-.677q0-.375-.158-.677a1.2 1.2 0 0 0-.46-.477q-.3-.18-.732-.179m.545 1.333a.8.8 0 0 1-.085.38.57.57 0 0 1-.238.241.8.8 0 0 1-.375.082H.788V12.48h.66q.327 0 .512.181.185.183.185.522m1.217-1.333v3.999h1.46q.602 0 .998-.237a1.45 1.45 0 0 0 .595-.689q.196-.45.196-1.084 0-.63-.196-1.075a1.43 1.43 0 0 0-.589-.68q-.396-.234-1.005-.234zm.791.645h.563q.371 0 .609.152a.9.9 0 0 1 .354.454q.118.302.118.753a2.3 2.3 0 0 1-.068.592 1.1 1.1 0 0 1-.196.422.8.8 0 0 1-.334.252 1.3 1.3 0 0 1-.483.082h-.563zm3.743 1.763v1.591h-.79V11.85h2.548v.653H7.896v1.117h1.606v.638z"/>
                                                            </svg>
                                                        </div>

                                                        <div v-else class="">
                                                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-file-earmark-word" viewBox="0 0 16 16">
                                                            <path d="M5.485 6.879a.5.5 0 1 0-.97.242l1.5 6a.5.5 0 0 0 .967.01L8 9.402l1.018 3.73a.5.5 0 0 0 .967-.01l1.5-6a.5.5 0 0 0-.97-.242l-1.036 4.144-.997-3.655a.5.5 0 0 0-.964 0l-.997 3.655L5.485 6.88z"/>
                                                            <path d="M14 14V4.5L9.5 0H4a2 2 0 0 0-2 2v12a2 2 0 0 0 2 2h8a2 2 0 0 0 2-2M9.5 3A1.5 1.5 0 0 0 11 4.5h2V14a1 1 0 0 1-1 1H4a1 1 0 0 1-1-1V2a1 1 0 0 1 1-1h5.5z"/>
                                                            </svg>
                                                        </div>
                                                        
                                                    </div>

                                                    <span class="font-semibold text-ellipsis max-w-60 whitespace-nowrap overflow-hidden">{{ file.name }}</span>

                                                    <div>{{ formatSize(file.size) }}</div>
                                                    <Badge value="Pending" severity="warn" />
                                                    <Button icon="pi pi-times" @click="onRemoveTemplatingFile(file, removeFileCallback, index)" outlined rounded severity="danger" />
                                                </div>
                                            </div>
                                        </div>

                                        <div v-if="uploadedFiles.length > 0">
                                            <h5>Completed</h5>
                                            <div class="flex flex-wrap gap-4">
                                                <div v-for="(file, index) of uploadedFiles" :key="file.name + file.type + file.size" class="p-8 rounded-border flex flex-col border border-surface items-center gap-4">
                                                    <div> 
                                                        <div v-if="file.type == 'application/pdf'" class="">
                                                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-filetype-pdf" viewBox="0 0 16 16">
                                                            <path fill-rule="evenodd" d="M14 4.5V14a2 2 0 0 1-2 2h-1v-1h1a1 1 0 0 0 1-1V4.5h-2A1.5 1.5 0 0 1 9.5 3V1H4a1 1 0 0 0-1 1v9H2V2a2 2 0 0 1 2-2h5.5zM1.6 11.85H0v3.999h.791v-1.342h.803q.43 0 .732-.173.305-.175.463-.474a1.4 1.4 0 0 0 .161-.677q0-.375-.158-.677a1.2 1.2 0 0 0-.46-.477q-.3-.18-.732-.179m.545 1.333a.8.8 0 0 1-.085.38.57.57 0 0 1-.238.241.8.8 0 0 1-.375.082H.788V12.48h.66q.327 0 .512.181.185.183.185.522m1.217-1.333v3.999h1.46q.602 0 .998-.237a1.45 1.45 0 0 0 .595-.689q.196-.45.196-1.084 0-.63-.196-1.075a1.43 1.43 0 0 0-.589-.68q-.396-.234-1.005-.234zm.791.645h.563q.371 0 .609.152a.9.9 0 0 1 .354.454q.118.302.118.753a2.3 2.3 0 0 1-.068.592 1.1 1.1 0 0 1-.196.422.8.8 0 0 1-.334.252 1.3 1.3 0 0 1-.483.082h-.563zm3.743 1.763v1.591h-.79V11.85h2.548v.653H7.896v1.117h1.606v.638z"/>
                                                            </svg>
                                                        </div>

                                                        <div v-else class="">
                                                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-file-earmark-word" viewBox="0 0 16 16">
                                                            <path d="M5.485 6.879a.5.5 0 1 0-.97.242l1.5 6a.5.5 0 0 0 .967.01L8 9.402l1.018 3.73a.5.5 0 0 0 .967-.01l1.5-6a.5.5 0 0 0-.97-.242l-1.036 4.144-.997-3.655a.5.5 0 0 0-.964 0l-.997 3.655L5.485 6.88z"/>
                                                            <path d="M14 14V4.5L9.5 0H4a2 2 0 0 0-2 2v12a2 2 0 0 0 2 2h8a2 2 0 0 0 2-2M9.5 3A1.5 1.5 0 0 0 11 4.5h2V14a1 1 0 0 1-1 1H4a1 1 0 0 1-1-1V2a1 1 0 0 1 1-1h5.5z"/>
                                                            </svg>
                                                        </div>
                                                        
                                                    </div>
                                                    <span class="font-semibold text-ellipsis max-w-60 whitespace-nowrap overflow-hidden">{{ file.name }}</span>
                                                    <div>{{ formatSize(file.size) }}</div>
                                                    <Badge value="Completed" class="mt-4" severity="success" />
                                                    <Button icon="pi pi-times" @click="removeUploadedFileCallback(index)" outlined rounded severity="danger" />
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </template>
                                <template #empty>
                                    <div class="flex items-center justify-center flex-col">
                                        <i class="pi pi-cloud-upload !border-2 !rounded-full !p-8 !text-4xl !text-muted-color" />
                                        <p class="mt-6 mb-10 text-sm md:text-md">Drag and drop files to here to upload.</p>
                                    </div>
                                </template>
                            </FileUpload>

                            <div  class="w-full mt-10 flex justify-end">
                                <PrimaryButton @click="submitDocuments()" class=" lowercase ">Submit Documents</PrimaryButton>
                            </div>
                        </div>
                        
                    </div>
                </div>

            <div v-if="all_training_materials.length" class="w-full mt-16 border border-gray-100 rounded-lg px-8">
                <div class="py-3 mt-6 mb-10">
                    <div class="w-full flex items-center justify-between">
                        <h1 class="text-md md:text-xl lg:text-2xl font-bold">Old Training Material</h1>

                        <button @click="deleteMaterial()"  v-if="!currentlySelectedMaterial" class="text-white bg-red-400 rounded-md py-2 px-5 lg:px-6 text-sm md:text-md hover:opacity-90">Delete Selected </button>
                    </div>
                </div>
                
                <div class="w-full">
                    <DataTable  
                    
                    v-model:selection="form.selectedMaterial" :value="all_training_materials" dataKey="id" scrollable class="w-[280px] md:w-full ">
                        <Column selectionMode="multiple"  headerStyle="width: 3rem"></Column>
                        <Column field="material_title" header="Training Material" style="min-width: 150px"></Column>
                        <Column field="material_type" header="Type" style="min-width: 150px"></Column>
                        <Column field="charecter_count" header="Characters" style="min-width: 150px"></Column>
                        <Column field="last_trained" header="Last Trained" style="min-width: 150px"></Column>
                    </DataTable>
                </div>
            </div>

            <div v-else class="w-full border border-gray-200 rounded-lg py-10 flex justify-center flex-col items-center mt-16">
                No Data Added!
            </div>
        </div>
    </AppLayout>

    <ConfirmDialog></ConfirmDialog>

</template>

<script> 
import AppLayout from '@/Layouts/AppLayout.vue'; 
import Icon from '@/Components/Global/Icon.vue'    
import {  Link } from '@inertiajs/vue3'; 
import DataTable from 'primevue/datatable';
import Column from 'primevue/column';
import FileUpload from 'primevue/fileupload';
import Toast from 'primevue/toast';
import ProgressBar from 'primevue/progressbar';
import Message from 'primevue/message';
import TextInput from '@/Components/TextInput.vue'; 
import LargeSpinner from '@/Components/Global/LargeSpinner.vue';
import PrimaryButton from '@/Components/PrimaryButton.vue';
import 'primeicons/primeicons.css' 
import Button from 'primevue/button';
import Badge from 'primevue/badge';
import OverlayBadge from 'primevue/overlaybadge';
import ConfirmDialog from 'primevue/confirmdialog'; 
import 'primeicons/primeicons.css' 

export default {
    components :{ 
        AppLayout,
        Icon,
        Link, 
        DataTable,
        Column,
        FileUpload,
        Toast,
        ProgressBar,
        Message,
        TextInput,
        LargeSpinner,
        PrimaryButton,
        Button,
        Badge,
        OverlayBadge,
        ConfirmDialog
    },

    props : {
        currently_selected_chatbot : Object ,
        training_materials : Array ,
    },

    data() {
        return {  
            embed_type : 1 ,
            all_training_materials: this.training_materials,
            form : {
                selectedMaterial: null,
                data_transfer_type : null,
                website: '',
                fetchedLinks : [], 
                chatbot_id : this.currently_selected_chatbot.id

            },

            loading : false,
            linksError : false,
            totalSizePercent: 0,
            totalSize: 0,
            addMoreLinks: false,
            links_error: false,
            new_link: '',
        }
    },

    methods: { 
            selectDataTransferType(type_){
                this.form.data_transfer_type = type_;
            },  

            async getUrlLinks() {
                const url = '/get-links'; // Replace with actual URL for the Laravel controller endpoint
                this.loading = true;
                this.linksError = false;

                try {
                    // Send URL to server
                    const response = await axios.post(url,  this.form );

                    const uniqueArray = [...new Set(response.data.links)];

                    // Populate fetchedLinks with the response data
                    this.form.fetchedLinks = uniqueArray;

                    this.form.fetchedLinks.push(this.form.website);
    
                } catch (error) {
                    console.error('Error fetching links:', error);
                    this.linksError = true;
                } finally {
                    this.loading = false;
                }
            },

            submitDocuments(){
                this.$inertia.post(route('submit_training_documents'), this.form , { 
                    preserveState: false, 
                    
                });
            },

            removeLink(index){
                this.form.fetchedLinks.splice(index, 1);
            },

            addNewLinkToList(){

                this.form.fetchedLinks.push(this.new_link);
                this.new_link = '';
                this.addMoreLinks = false;

            },

            onRemoveTemplatingFile(file, removeFileCallback, index) {
                removeFileCallback(index);
                this.totalSize -= parseInt(this.formatSize(file.size));
                this.totalSizePercent = this.totalSize / 10;
            },

            onSelectedFiles(event) {
                this.form.files = event.files;
                this.form.files.forEach((file) => {
                    this.totalSize += parseInt(this.formatSize(file.size));
                });
            },
            
            onTemplatedUpload() {
                this.$toast.add({ severity: 'info', summary: 'Success', detail: 'File Uploaded', life: 3000 });
            },

            formatSize(bytes) {
                const k = 1024;
                const dm = 3;
                const sizes = this.$primevue.config.locale.fileSizeTypes;

                if (bytes === 0) {
                    return `0 ${sizes[0]}`;
                }

                const i = Math.floor(Math.log(bytes) / Math.log(k));
                const formattedSize = parseFloat((bytes / Math.pow(k, i)).toFixed(dm));

                return `${formattedSize} ${sizes[i]}`;
            },

            submitTrainingLinks(){
                this.$inertia.post(route('submit_training_links'), this.form , { 
                    preserveState: false, 
                    
                });
            },

        deleteMaterial() {
            this.$confirm.require({
                message: 'Do you want to delete selected material?',
                header: 'Delete Trainig Material',
                icon: 'pi pi-info-circle',
                rejectProps: {
                    label: 'Cancel',
                    severity: 'secondary',
                    outlined: true
                },
                acceptProps: {
                    label: 'Delete',
                    severity: 'danger'
                },

                accept: () => {
                    this.$inertia.post(route('delete_training_material'), this.form , { 
                        preserveState: false, 
                    });
                },
                reject: () => {
                    
                }
            });
        }

        },

    computed : {
        currentlySelectedMaterial(){
            return this.form.selectedMaterial == null || this.form.selectedMaterial == '';
        }
    }
}
</script>