<template>
    <Head title="Welcome" />

    <Header></Header>

    <section class="w-full lg:h-screen flex flex-col justify-center">
        <div class="w-full flex flex-col lg:flex-row items-center lg:space-x-10">
            <div class="w-full lg:w-[50%] flex flex-col items-center lg:items-start lg:pl-20 px-5 lg:px-0 mt-10 lg:mt-0">
                
                <h1 class="text-center lg:text-left text-2xl lg:text-4xl font-bold">Turn Your Business Data Into a 24/7 AI Assistant</h1>
        
                <p class="text-center lg:text-left text-md lg:text-lg text-gray-600 mt-8">Effortlessly create a custom chatbot that answers questions, supports customers, and boosts engagement — all powered by your own content, no coding needed.
                </p>
         
        
                <div class="mt-10 flex items-center space-x-3">
                    <Link :href="route('register')" class="lg:px-5 px-2 py-3 rounded-md lg:text-md text-sm font-semibold flex items-center bg-gradient-to-r from-primaryColor to-secondaryColor hover:opacity-80 focus:opacity-80 text-white">
                        <span>Register Now!</span>
                    </Link>

                    <Link :href="route('login')" class="lg:px-5 px-2 py-3 rounded-md border border-gray-300 hover:bg-gray-200 focus:bg-gray-200 lg:text-md text-sm font-semibold flex items-center bg-gray-50 ">
                        <span>Login</span>
                    </Link>
                </div>
                
            </div>
    
            <div class="w-full lg:w-[50%] mt-16 lg:mt-0 flex justify-center px-5 lg:px-0">
                <div class="w-full lg:w-[350px] shadow-lg bg-white h-[400px] lg:h-[460px] flex-shrink-0 max-h-[460px] rounded-2xl">
     
                    <div class="w-full bg-gradient-to-r from-primaryColor to-secondaryColor flex justify-between items-center rounded-t-3xl h-[15%] px-5">
                        <h1 class="text-md font-extrabold text-white">Botomatic</h1>
    
                        <div class="rounded-full border border-white px-2 py-1 flex items-center space-x-2 text-white text-xs"> 
                            <svg xmlns="http://www.w3.org/2000/svg" class="text-white h-4" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"  >
                                <path stroke-linecap="round" stroke-linejoin="round" d="M17.982 18.725A7.488 7.488 0 0 0 12 15.75a7.488 7.488 0 0 0-5.982 2.975m11.963 0a9 9 0 1 0-11.963 0m11.963 0A8.966 8.966 0 0 1 12 21a8.966 8.966 0 0 1-5.982-2.275M15 9.75a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z" />
                            </svg>
                            profile
                        </div>
                    </div>
    
                    <div class="w-full h-[85%] rounded-b-3xl  flex flex-col justify-between" >
                        <div class="w-full h-[82%] px-4 py-6 overflow-y-auto" ref="messagesContainer">
    
                            <div class="w-full flex items-end space-x-3">
                                <div  class="flex-shrink-0 rounded-full w-8 h-8 p-1.5 flex justify-center flex-col items-center bg-primaryColor">
                                    <img src="/img/bot.png"  alt="">
                                </div>
    
                                <div class="bg-gray-200 rounded-3xl p-4">
                                    <p class="text-xs text-gray-800">Welcome 👋! How can we help you today? </p>
                                </div>
                            </div> 
    
                        </div>
    
                        <div class="w-full min-h-[20%] p-3 py-2 border-t border-gray-200 flex items-center " style="max-height: 100px;">
                            <div style="max-height: 90px;"  class="w-[85%] rounded-lg border-2 border-primaryColor p-1 flex items-center ">
                                <textarea 
                                type="text" 
                                style= "resize: none; 
                                max-height: 80px;"
                                ref="message"   
                                
                                class="w-[90%] resize-none max-h-full border-none focus:ring-0 text-xs text-gray-700" placeholder="Type a message..."></textarea>
                                
                                <button  
     
                                class="w-[12%] h-6 rounded-full flex flex-col items-center justify-center group hover:bg-primaryColor">
                                    <svg class="w-4 group-hover:text-white"xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"  >
                                        <path stroke-linecap="round" stroke-linejoin="round" d="M6 12 3.269 3.125A59.769 59.769 0 0 1 21.485 12 59.768 59.768 0 0 1 3.27 20.875L5.999 12Zm0 0h7.5" />
                                    </svg>
                                </button>
                            </div>
    
                            <button class="w-[15%] flex justify-center hover:animate-spin "> 
                                <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4 text-primaryColor group-hover:text-white " fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"  >
                                    <path stroke-linecap="round" stroke-linejoin="round" d="M16.023 9.348h4.992v-.001M2.985 19.644v-4.992m0 0h4.992m-4.993 0 3.181 3.183a8.25 8.25 0 0 0 13.803-3.7M4.031 9.865a8.25 8.25 0 0 1 13.803-3.7l3.181 3.182m0-4.991v4.99" />
                                </svg>
                            </button>
                        </div>
                    </div>
    
                </div>
            </div>
        </div>
    </section>
   
    
    <section class="w-full bg-gray-50 py-24 lg:py-40 lg:px-20 px-5 items-center justify-between flex flex-col mt-32 space-y-16 md:space-y-20">
        <div class="lg:w-3/5">
            <span class="lg:text-3xl text-2xl text-center  font-bold flex items-center justify-center text-gray-700">You're three easy steps away from your own personalized AI support chatbot</span>
        </div>
        
        <div class="w-full flex flex-col lg:flex-row space-y-10 lg:space-y-0 lg:items-stretch lg:space-x-10 ">
            <div class="w-full lg:w-1/3 flex flex-col">
                <div class="bg-primaryColor w-12 md:w-14 h-12 md:h-14 rounded-full flex items-center text-md md:text-xl text-white font-bold justify-center">
                    <span>1</span>
                </div>

                <h3 class="text-xl md:text-2xl font-bold mt-6">Sign Up & Onboard</h3>
                <p class="text-md text-gray-500 mt-3">Create an account in seconds and provide text-sm md:basic details about your business. Our AI will guide you through a simple onboarding process.</p>
            </div>

            <div class="w-full lg:w-1/3 flex flex-col">
                <div class="bg-primaryColor w-12 md:w-14 h-12 md:h-14 rounded-full flex items-center text-md md:text-xl text-white font-bold justify-center">
                    <span>2</span>
                </div>
                <span class="text-xl md:text-2xl font-bold mt-6">Build & Customize Your Bot</span>
                <p class="text-sm md:text-md text-gray-500 mt-3">Upload your documents, FAQs, and other resources. Customize your bot’s responses, branding, and personality—no coding required.</p>
            </div>

            <div class="w-full lg:w-1/3 flex flex-col">
                <div class="bg-primaryColor w-12 md:w-14 h-12 md:h-14 rounded-full flex items-center text-md md:text-xl text-white font-bold justify-center">
                    <span>3</span>
                </div>
                <span class="text-xl md:text-2xl font-bold mt-6">Install on your site</span>
                <p class="text-sm md:text-md text-gray-500 mt-3">Embed your AI chatbot on your website, app, help center, or any platform you choose. Engage customers wherever they need support.</p>
            </div>
        </div>
    </section>
    
      
    <section id="reviews" class="w-full py-16 md:py-20 lg:py-28 ">
        <div class="w-full flex justify-center">
            <h1 class="text-2xl md:text-3xl lg:text-4xl font-bold text-gray-600 text-center">Custom AI that acts and sounds just like you do</h1>
        </div>

        <div class="w-full flex justify-center mt-10 md:mt-12 lg:mt-16 px-2 lg:px-0">
            <img src="/img/AI.png" class="w-full lg:w-[70%]" />
        </div>
    </section>


    <section id="reviews" class="w-full py-28 px-5 lg:px-20">
        <div class="w-full flex justify-center">
            <h1 class="text-center text-2xl md:text-3xl lg:text-4xl font-bold">What people are saying about <span class="text-primaryColor">Botomatic</span></h1>
        </div>

        <div class="w-full mt-16 lg:mt-20 flex flex-col lg:flex-row lg:items-stretch lg:space-x-6">
            <div class="w-full lg:w-1/3 min-h-full rounded-xl border-t-[20px] border-primaryColor/40 pt-8 pb-12 px-6 shadow-md mb-7 lg:mb-0">
                <div class="flex items-center justify-between">
                    <div class="flex flex-col items-start">
                        <h1 class="text-md md:text-lg font-bold">Sarah J.</h1>
                        <p class="text-sm md:text-md">Customer Success Manager</p>
                    </div>

                    <div class="w-12 md:w-16 h-12 md:h-16 rounded-full flex-shrink-0">
                        <img src="/img/reviews/sarah.webp" class="w-full object-cover object-center h-full rounded-full" alt="">
                    </div>
                </div>

                <p class="text-sm md:text-md mt-5">"Botomatic transformed our customer support! Our response times dropped, and customer satisfaction skyrocketed. A game changer for our business!"
                </p>
            </div>

            <div class="w-full lg:w-1/3 min-h-full rounded-xl border-t-[20px] border-primaryColor/40 pt-8 pb-12 px-6 shadow-md mb-7 lg:mb-0">
                <div class="flex items-center justify-between">
                    <div class="flex flex-col items-start">
                        <h1 class="text-md md:text-lg font-bold">Hua Y.</h1>
                        <p class="text-sm md:text-md">Graphic Designer</p>
                    </div>

                    <div class="w-12 md:w-16 h-12 md:h-16 rounded-full flex-shrink-0">
                        <img src="/img/reviews/hua.webp" class="w-full object-cover object-center h-full rounded-full" alt="">
                    </div>
                </div>

                <p class="text-sm md:text-md mt-5">"I set up my AI bot in minutes, and it started answering common questions right away. It saved my team countless hours of manual work."</p>
            </div>

            <div class="w-full lg:w-1/3 min-h-full rounded-xl border-t-[20px] border-primaryColor/40 pt-8 pb-12 px-6 shadow-md mb-7 lg:mb-0">
                <div class="flex items-center justify-between">
                    <div class="flex flex-col items-start">
                        <h1 class="text-md md:text-lg font-bold">Emily C.</h1>
                        <p class="text-sm md:text-md">Marketing Lead</p>
                    </div>

                    <div class="w-12 md:w-16 h-12 md:h-16 rounded-full flex-shrink-0">
                        <img src="/img/reviews/emily.webp" class="w-full object-cover object-center h-full rounded-full" alt="">
                    </div>
                </div>

                <p class="text-sm md:text-md mt-5">"The customization options are fantastic! We made the bot feel like a natural extension of our brand, and our users love it"
                </p>
            </div>
        </div>

        <div class="w-full lg:mt-20 flex flex-col lg:flex-row  lg:items-stretch lg:space-x-6">
            <div class="w-full lg:w-1/3 min-h-full rounded-xl border-t-[20px] border-primaryColor/40 pt-8 pb-12 px-6 shadow-md mb-7 lg:mb-0">
                <div class="flex items-center justify-between">
                    <div class="flex flex-col items-start">
                        <h1 class="text-md md:text-lg font-bold">Daniel L.</h1>
                        <p class="text-sm md:text-md">Head of Support</p>
                    </div>

                    <div class="w-12 md:w-16 h-12 md:h-16 rounded-full flex-shrink-0">
                        <img src="/img/reviews/daniel.webp" class="w-full object-cover object-center h-full rounded-full" alt="">
                    </div>
                </div>

                <p class="text-sm md:text-md mt-5">"Botomatic helped us scale our support without hiring more agents. The bot handles routine queries, so our team can focus on complex issues."
                </p>
            </div>

            <div class="w-full lg:w-1/3 min-h-full rounded-xl border-t-[20px] border-primaryColor/40 pt-8 pb-12 px-6 shadow-md mb-7 lg:mb-0">
                <div class="flex items-center justify-between">
                    <div class="flex flex-col items-start">
                        <h1 class="text-md md:text-lg font-bold">Jessica M.</h1>
                        <p class="text-sm md:text-md">Product Manager</p>
                    </div>

                    <div class="w-12 md:w-16 h-12 md:h-16 rounded-full flex-shrink-0">
                        <img src="/img/reviews/jessica.webp" class="w-full object-cover object-center h-full rounded-full" alt="">
                    </div>
                </div>

                <p class="text-sm md:text-md mt-5">"I was amazed at how easy it was to train the bot with our knowledge base. It’s like having an always-on expert for our customers."</p>
            </div>

            <div class="w-full lg:w-1/3 min-h-full rounded-xl border-t-[20px] border-primaryColor/40 pt-8 pb-12 px-6 shadow-md mb-7 lg:mb-0">
                <div class="flex items-center justify-between">
                    <div class="flex flex-col items-start">
                        <h1 class="text-md md:text-lg font-bold">Carlos A.</h1>
                        <p class="text-sm md:text-md">Founder & CEO</p>
                    </div>

                    <div class="w-12 md:w-16 h-12 md:h-16 rounded-full flex-shrink-0">
                        <img src="/img/reviews/carlos.webp" class="w-full object-cover object-center h-full rounded-full" alt="">
                    </div>
                </div>

                <p class="text-sm md:text-md mt-5">"Botomatic gave our small business a huge advantage. It’s like having a full-time assistant who never takes a break."</p>
            </div>
        </div>
    </section>

    <section class="w-full my-10 md:my-16 lg:my-24 px-6 lg:px-28">
        <div class="w-full flex justify-center ">
            <div class="lg:w-4/5">
                <h1 class="text-2xl lg:text-3xl font-bold text-center">Frequently asked questions</h1>
            </div>
        </div>
 
        <div class="w-full mt-16 flex flex-col lg:flex-row lg:space-y-0 space-y-6 lg:items-start lg:space-x-8" >
            <div class="w-full lg:w-1/2 flex flex-col space-y-8 "  >
                <button v-for="(faq, index) in faqs1" :key="index" @click="faq.open = !faq.open " class="w-full shadow-md hover:cursor-pointer rounded-lg px-6 lg:px-10 py-6 lg:py-8  border border-gray-100">
                    <div class="flex items-center justify-between">
                        <div class="text-left flex-1 text-md lg:text-lg">
                            {{ faq.question }}
                        </div>
                        <div class="w-5 lg:w-8 flex justify-end">
                            <span v-if="faq.open == true">
                                <svg xmlns="http://www.w3.org/2000/svg"  fill="currentColor" class="bi bi-chevron-down w-4 text-gray-500" viewBox="0 0 16 16">
                                    <path fill-rule="evenodd" d="M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708"/>
                                </svg>
                            </span>

                            <span v-if="faq.open == false">
                                <svg xmlns="http://www.w3.org/2000/svg" fill="currentColor" class="bi bi-chevron-up w-4 text-gray-500" viewBox="0 0 16 16">
                                    <path fill-rule="evenodd" d="M7.646 4.646a.5.5 0 0 1 .708 0l6 6a.5.5 0 0 1-.708.708L8 5.707l-5.646 5.647a.5.5 0 0 1-.708-.708z"/>
                                </svg>
                            </span>
                        </div>
                    </div>

                    <div v-if="faq.open == true" class="w-full mt-4 ">
                        <p class="text-sm lg:text-md text-gray-500 text-left">{{faq.answer}}</p>
                    </div>
                </button>
            </div>
            
            <div class="w-full lg:w-1/2 flex flex-col space-y-8 mt-10 lg:mt-0"  >
                <button v-for="(faq, index) in faqs2" :key="index" @click="faq.open = !faq.open " class="w-full shadow-md hover:cursor-pointer rounded-lg px-6 lg:px-10 py-6 lg:py-8  border border-gray-100">
                    <div class="flex items-center justify-between">
                        <div class="text-left flex-1 text-sm lg:text-lg">
                            {{ faq.question }}
                        </div>
                        <div class="w-5 lg:w-8 flex justify-end">
                            <span v-if="faq.open == true">
                                <svg xmlns="http://www.w3.org/2000/svg"  fill="currentColor" class="bi bi-chevron-down w-4 text-gray-500" viewBox="0 0 16 16">
                                    <path fill-rule="evenodd" d="M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708"/>
                                </svg>
                            </span>

                            <span v-if="faq.open == false">
                                <svg xmlns="http://www.w3.org/2000/svg" fill="currentColor" class="bi bi-chevron-up w-4 text-gray-500" viewBox="0 0 16 16">
                                    <path fill-rule="evenodd" d="M7.646 4.646a.5.5 0 0 1 .708 0l6 6a.5.5 0 0 1-.708.708L8 5.707l-5.646 5.647a.5.5 0 0 1-.708-.708z"/>
                                </svg>
                            </span>
                        </div>
                    </div>

                    <div v-if="faq.open == true" class="w-full mt-4 ">
                        <p class="text-sm lg:text-md text-gray-500 text-left">
                            {{ faq.answer }}
                        </p>
                    </div>
                </button>
            </div> 
        </div>
    </section>

    <section class="w-full px-4 lg:px-20 -mb-20 mt-28 py-20 bg-primaryColor/20 ">
        <div class="w-full flex justify-center">
            <h1 class="text-2xl lg:text-3xl font-bold">Top picks from our blog</h1>
        </div>
            
        <div class="mt-12 mb-20 lg:mb-32 flex flex-col lg:flex-row lg:space-x-6 lg:items-stretch">
            <Link v-for="(post, index) in $page.props.posts" :key="index" :href="route('view_blog', post.slug)" class="lg:min-h-full w-full lg:w-1/3 flex flex-col justify-between shadow bg-white rounded-md px-6 py-8 mb-6 lg:mb-0">
                <div>
                    <div class="flex items-center space-x-2">
                        <span class="text-primaryColor text-sm md:text-md">Info</span>
                        <span class="text-xl font-bold text-gray-500">•</span> 
                        <span class="text-gray-500 text-sm md:text-md">{{post.duration}}</span>
                    </div>
    
                    <h1 class="mt-3 text-lg md:text-xl font-bold text-gray-600">{{post.title}}</h1>
    
                    <p class="mt-4 text-sm md:text-md text-gray-500 tracking-wide">
                        {{post.excerpt}}
                    </p>
                </div>

                <div class="mt-8 flex items-center space-x-2">
                    <div class="w-10 md:w-12 h-10 md:h-12 rounded-full bg-gray-100">
                        <img src="/img/logos/logo.webp" class="rounded-full w-full h-full object-cover object-center" alt="">
                    </div>
                    <div class="flex flex-col items-start">
                        <h1 class="text-sm md:text-md font-bold text-gray-800">Stephen</h1>
                        <p class="text-gray-400 text-xs md:text-sm">Head Of Content</p>
                    </div>
                </div>
            </Link> 
 
        </div> 
    </section>
 
    <Footer></Footer> 
</template>
 

<script>
import { Head, Link } from '@inertiajs/vue3';
import Icon from '@/Components/Global/Icon.vue';
import Footer from '@/Components/Landing/Footer.vue';
import Header from '@/Components/Landing/Header.vue';

export default {
    components :{
        Head, Link,
        Icon,
        Footer,
        Header,
    },

    data(){
        return { 
            faqs1: [
                    {
                        open: false,
                        question: 'What is Botomatic?',
                        answer: 'Botomatic is an AI-powered platform that lets you upload your business data and instantly create a chatbot to handle customer questions on your website or app.',
                    },
                    {
                        open: false,
                        question: 'How does Botomatic work?',
                        answer: 'Simply upload your documents, FAQs, or resources, and Botomatic will use that data to train a bot that can provide accurate answers to users in real-time.',
                    },
                    {
                        open: false,
                        question: 'Can I customize my bot’s responses?',
                        answer: 'Yes! You can fine-tune your bot’s responses, define its tone, and even add custom fallback messages to ensure a personalized experience.',
                    },
            ],

            faqs2: [
                    {
                        open: false,
                        question: 'Is Botomatic suitable for small businesses?',
                        answer: 'Absolutely! Botomatic is designed to scale, making it perfect for startups, small businesses, and large enterprises alike.',
                    },
                    {
                        open: false,
                        question: 'What platforms can I deploy my bot on?',
                        answer: 'You can deploy your bot on websites, social media platforms, and messaging apps, or even use it internally for team support.',
                    },
                    {
                        open: false,
                        question: 'Is my data secure with Botomatic?',
                        answer: 'Yes! We use advanced encryption and security measures to protect your data and ensure your bots only access what you allow.',
                    },
            ]
        }
    },
 
}

</script>