<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <title inertia>{{ config('app.name', 'Laravel') }}</title>

        <meta name="title" content="Kimana - Instantly Create AI Chatbots for Your Business">
        <meta name="description" content="<PERSON><PERSON> lets you turn your company’s knowledge into an AI-powered chatbot in minutes. Upload your data, customize responses, and deploy your bot anywhere—no coding required.">
        <meta name="keywords" content="AI chatbot, chatbot builder, AI automation, business chatbot, customer support AI, AI assistant, chatbot for websites, AI-powered chatbot, no-code chatbot, Kimana">
        <meta name="robots" content="index, follow">
        <meta name="author" content="Kimana">
        <meta name="language" content="English">

        <!-- Social Media Meta Tags -->
        <meta property="og:title" content="<PERSON>ana - Instantly Create AI Chatbots for Your Business">
        <meta property="og:description" content="Transform your website or app with an AI chatbot that answers questions, assists customers, and streamlines support. Upload your data and get started instantly!">
        <meta property="og:image" content="https://www.getkimana.com/preview.png">
        <meta property="og:url" content="https://getkimana.com">
        <meta name="twitter:card" content="summary_large_image">
        <meta name="twitter:title" content="Kimana - Instantly Create AI Chatbots for Your Business">
        <meta name="twitter:description" content="Kimana turns your company’s data into a smart AI chatbot that enhances customer interactions. Get started today!">
        <meta name="twitter:image" content="https://getkimana.com/logo.png">
        <link rel="canonical" href="https://getkimana.com">
        <!-- Fonts -->
        <link rel="preconnect" href="https://fonts.bunny.net">
        <link href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" rel="stylesheet" />

        <link rel="shortcut icon" href="https://getkimana.com/logo.png" type="image/x-icon"/>

        
        <!-- Scripts -->
        @routes
        @vite(['resources/js/app.js', "resources/js/Pages/{$page['component']}.vue"])
        @inertiaHead 

        <style > 
            body {
              /* Set the width and height of the scrollbar */
              overflow-y: scroll;
              overflow-x: hidden;
              scrollbar-width: thin;
              scrollbar-color: #ddd #fff;
            }
            
            /* Track */
            ::-webkit-scrollbar {
              width: 10px;
              height: 10px;
              max-height: 100px;
            }
            
            /* Handle */
            ::-webkit-scrollbar-thumb {
              background-color: #aaa ;
              border-radius: 4px;
            }
            
            /* Handle on hover */
            ::-webkit-scrollbar-thumb:hover {
              background-color: #4682B4 ;
            }
            
        </style>

        <script>
            document.documentElement.classList.remove("dark");
        </script>

    </head>
    <body class="font-sans antialiased">

        @inertia
    </body>
</html>
