<?php

namespace App\Http\Controllers;

use Inertia\Inertia;
use App\Models\Chatbot;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class AppearanceController extends Controller
{ 
    public function appearance() {
 
        return Inertia::render('Appearance/Appearance', [ 
            'chatbots' => $this->chatbots(),
            'currently_selected_chatbot' => $this->selectedBot(),
        ]);
        
    } 
    
    public function save_appearance_settings(Request $request) {

        $chatbot = Chatbot::where('id', auth()->user()->currently_selected_chatbot)->first(); 

        $number_of_times_before_asking = $request->number_of_times_before_asking;

        if (is_array($request->number_of_times_before_asking)){
          
            $number_of_times_before_asking = $request->number_of_times_before_asking['number'];
        } 


        if (is_array($request->number_of_times_to_ask)){
            $number_of_times_to_ask = $request->number_of_times_to_ask['number'];
        } else {
            $number_of_times_to_ask = $request->number_of_times_to_ask;
        }

        $chatbot->forceFill([
                'chatbot_title' => $request->chatbot_title,
                'bot_name' => $request->bot_name,
                'chatbot_colour' => $request->chatbot_colour,
                'welcome_message' => $request->welcome_message['message'],
                'widget_button_position' => $request->widget_button_position,
                'show_contact_form' => $request->show_contact_form,
                'number_of_times_before_asking' => $number_of_times_before_asking,
                'number_of_times_to_ask' => $number_of_times_to_ask,
                'contact_form_title' => $request->contact_form_title,
        ])->save();

        if ($request->hasFile('profile_avatar')) {
            $oldAvatarPath = $chatbot->chatbot_avatar;
        
            $chatbot->chatbot_avatar = $this->handleProfileAvatar($request->file('profile_avatar'), $oldAvatarPath);
        }
        
        if ($request->hasFile('widget_button_icon')) {
            $oldWidgetIconPath = $chatbot->widget_button_icon;
        
            $chatbot->widget_button_icon = $this->handleWidgetPhoto($request->file('widget_button_icon'), $oldWidgetIconPath);
        }

        $chatbot->save();

        }

        private function handleProfileAvatar($photo, $oldPhotoPath = null)
        {
            if ($oldPhotoPath && Storage::disk('public')->exists($oldPhotoPath)) {
                Storage::disk('public')->delete($oldPhotoPath);
            }
        
            $filename = 'avatar_' . time() . '.' . $photo->getClientOriginalExtension();
            $path = $photo->storeAs('avatars', $filename, 'public');
            return $path;
        }

        private function handleWidgetPhoto($photo, $oldPhotoPath = null)
        {
            if ($oldPhotoPath && Storage::disk('public')->exists($oldPhotoPath)) {
                Storage::disk('public')->delete($oldPhotoPath);
            }
        
            $filename = 'widget_icon_' . time() . '.' . $photo->getClientOriginalExtension();
            $path = $photo->storeAs('widget_icons', $filename, 'public');
            return $path;
        }
    
}
