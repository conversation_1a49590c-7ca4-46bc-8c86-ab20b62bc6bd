<template>
    <div>
        <input ref="picker" name="imageInput" type="file" accept="image/*" class="hidden" multiple @change="change">
        
        <button @click="browseFiles" type="button" class="rounded-lg border border-gray-300 border-dashed bg-gray-100 flex flex-col items-center w-24 h-24 mt-2 p-5"> 
            <icon class="w-12 h-12" stroke-width="2" stroke="gray" name="camera"> </icon> 
        </button>
    </div>
</template>

<script>
    import Icon from '@/Components/Global/Icon.vue'  

    export default {
        components:{
            Icon
        },

        methods:{
            browseFiles(){
                this.$refs.picker.click();
            },

            change(e){
                this.$emit('input',e.target.files);
            }
        }
    }
</script>