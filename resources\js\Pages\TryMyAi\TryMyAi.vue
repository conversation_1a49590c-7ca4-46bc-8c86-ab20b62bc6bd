<template>
    <AppLayout title="Demo">
        <div class="min-h-screen p-5 lg:p-10 bg-gray-50"> 
            <h1 class="text-lg font-bold">Widget Demo ... {{ showContact }} || {{ this.chat.contacts_sent }}</h1>

            <div class="mt-6 w-full rounded-lg flex justify-center">
                <div class="w-[300px] lg:w-[340px] shadow-lg bg-white h-[450px] flex-shrink-0 max-h-[460px] rounded-2xl">
 
                    <div :style="{ backgroundColor: this.currently_selected_chatbot.chatbot_colour }" class="w-full flex justify-between items-center rounded-t-3xl h-[15%] px-5">
                        <h1 class="text-md font-extrabold text-white">{{ this.currently_selected_chatbot.chatbot_title }}</h1>

                        <div class="rounded-full border border-white px-2 py-1 flex items-center space-x-2 text-white text-xs">
                            <icon name="profile" class="text-white h-4"></icon>
                            profile
                        </div>
                    </div>

                    <div class="w-full h-[85%] rounded-b-3xl  flex flex-col justify-between" >
                        <div v-if="showContact"class="w-full h-[82%] p-5">
                            <form @submit.prevent="storeContact()"  
                                    style="transition: all 30s ease-in-out; z-index: 100000;"
                                    class="w-full h-full px-2  " 
                                    >
                                <div class="w-full h-full rounded-xl bg-gray-100 border border-gray-200 shadow-md px-5 py-5 mb-5">
    
                                    <div class="w-full flex justify-center">
                                        <h1 class="w-[80%] text-sm font-semibold text-center">{{ this.currently_selected_chatbot.contact_form_title }}</h1>
                                    </div>
    
                                    
                                    <TextInput
                                        id="name"
                                        v-model="form.name"
                                        type="text"
                                        class="mt-3 block w-full h-9 text-sm"
                                        placeholder="Name"
                                        required
                                        autofocus
                                        autocomplete="name"
                                        />
    
                                        
                                    <TextInput
                                        id="email"
                                        v-model="form.email"
                                        type="email"
                                        class="mt-3 block w-full h-9 text-sm"
                                        placeholder="Email"
                                        required
                                        autofocus
                                        autocomplete="email"
                                        />
    
                                    <div class="mt-4 w-full flex justify-end">    
                                        <PrimaryButton type="submit" class="bg-primaryCoor flex items-center">
                                            Save
                                            <icon name="chevron_right" class="w-3 ml-1"></icon>
                                        </PrimaryButton>
                                    </div>
                                </div>
                            </form>
                        </div>

                        <div v-else class="w-full h-[82%] px-4 py-6 overflow-y-auto relative" ref="messagesContainer"> 
 
                            <div v-if="currently_selected_chatbot.welcome_message != null "  class="w-full flex items-end space-x-3">
                                <div :style="{ backgroundColor: this.currently_selected_chatbot.chatbot_colour }"  class="flex-shrink-0 rounded-full w-8 h-8 p-1.5 flex justify-center flex-col items-center">
                                    <img src="/img/bot.png"  alt="">
                                </div>

                                <div class="bg-gray-200 rounded-3xl p-4">
                                    <p class="text-xs text-gray-800">{{ currently_selected_chatbot.welcome_message }} </p>
                                </div>
                            </div>

                            <div v-else  class="w-full flex items-end space-x-3">
                                <div :style="{ backgroundColor: this.currently_selected_chatbot.chatbot_colour }"  class="flex-shrink-0 rounded-full w-8 h-8 p-1.5 flex justify-center flex-col items-center">
                                    <img src="/img/bot.png"  alt="">
                                </div>

                                <div class="bg-gray-200 rounded-3xl p-4">
                                    <p class="text-xs text-gray-800">Welcome 👋! How can we help you today? </p>
                                </div>
                            </div>

                            <div v-for="(message, index) in messages"
                            :key="index"
                            class="w-full">      

                                <div v-if="message.role == 'assistant'"  class="w-full flex items-end space-x-3">
                                    <div :style="{ backgroundColor: this.currently_selected_chatbot.chatbot_colour }"  class="flex-shrink-0 rounded-full w-8 h-8 p-1.5 flex justify-center flex-col items-center">
                                        <img src="/img/bot.png"  alt="">
                                    </div>
    
                                    <div class="bg-gray-200 rounded-3xl p-4">
                                        <p class="text-xs text-gray-800">{{ Object.values(message.content)[0] }} </p>
                                    </div>
                                </div>
    
                                <div v-else-if="message.role == 'user'" class="w-full flex justify-end my-3">
                                    <div :style="{ backgroundColor: this.currently_selected_chatbot.chatbot_colour }"  class=" rounded-3xl p-3">
                                        <p class="text-xs text-white">{{ message.content }} </p>
                                    </div>
                                </div>
                            </div>

                            <div v-if="sending_message" class="w-full flex items-end space-x-3 mt-10">

                                <div :style="{ backgroundColor: this.currently_selected_chatbot.chatbot_colour }"  class="flex-shrink-0 rounded-full w-8 h-8 p-1.5 flex justify-center flex-col items-center">
                                    <img src="/img/bot.png"  alt="">
                                </div>

                                <div class="flex flex-col items-start">
                                    <div class="w-36 h-10 bg-gray-200 rounded-2xl p-4">
                                        <div class="ml-5 dot-typing"></div>
                                    </div>

                                    <div class="mt-1 ml-1">
                                        <span class="text-xs italic text-gray-500">
                                            Typing ...
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="w-full min-h-[20%] p-3 py-2 border-t border-gray-200 flex items-center " style="max-height: 100px;">
                            <div :style="{ borderColor : this.currently_selected_chatbot.chatbot_colour }"  style="max-height: 90px;"  class="w-[85%]  rounded-lg border-2 p-1 flex items-center  ">

                                <textarea 
                                v-model="local_message"
                                type="text" 
                                style= "resize: none; 
                                max-height: 80px;"
                                ref="message"  
                                @input="resizeTextarea()"
                                
                                class="w-[90%] resize-none max-h-full border-none focus:ring-0 text-xs text-gray-700" placeholder="Type a message..."></textarea>
                                
                                <button 
                                :style="{ backgroundColor: isHovered ? this.currently_selected_chatbot.chatbot_colour : '', color: isHovered ? 'white' : this.currently_selected_chatbot.chatbot_colour }" 
                                @click="sendMessage" 
                                :disabled="local_message == null || local_message == '' || sending_message == true"
                                class="w-[12%] h-7 rounded-full flex flex-col items-center justify-center group"
                                @mouseenter="isHovered = true"
                                @mouseleave="isHovered = false"
                                >
                                    <icon :style="{ color: isHovered ? 'white' : this.currently_selected_chatbot.chatbot_colour }"   name="send" class="w-4 group-hover:text-white"></icon>
                                </button>
                            </div>

                            <button :class="{'animate-spin' : deleteting_chat == true}" @click="deleteChat" class="w-[15%] flex justify-center ">
                                <icon :style="{ color: this.currently_selected_chatbot.chatbot_colour }" name="refresh" class="w-4 h-4 "></icon>

                            </button>
                        </div>
                    </div>

                </div>
            </div>
        </div>
    </AppLayout>
</template>

<script> 
import AppLayout from '@/Layouts/AppLayout.vue'; 
import Icon from '@/Components/Global/Icon.vue'    
import {  Link } from '@inertiajs/vue3'; 
import TextInput from '@/Components/TextInput.vue';
import PrimaryButton from '@/Components/PrimaryButton.vue';

export default {
    components :{ 
        AppLayout,
        Icon,
        Link, 
        TextInput,
        PrimaryButton
    },

    props : {
        currently_selected_chatbot : Object,
        all_messages : Array,
        chat : Object,
    },
    

    data() {
        return {  
            embed_type : 1,
            messages : this.all_messages ,
            messageForm : {
                message : ''
            },
            form : {
                name :'',
                email :'',
            },
            local_message : '',
            sending_message : false,
            isHovered : false,
            deleteting_chat : false,
            chat_contact_sent : false,
            contact_operation_done : false,

        }
    },

    methods: { 
        sendMessage() {
                this.sending_message = true;

                this.messageForm.message = this.local_message;
                
                this.chat_contact_sent = true;

                if (this.local_message !== '' && this.local_message !== null) {
                    
                    this.messages.push(
                        { role: 'user', content: this.messageForm.message }
                    );

                    this.local_message = '';

                    // this.$inertia.post(route('send_message_in_chat',this.chat.id), this.messageForm , { 
                    //     preserveState: false, 
                    // });
                    
                    axios.post(this.route('send_message_in_chat', this.chat.id), this.messageForm)
                        .then(({ data }) => {
                            this.messageForm.message = ''; 


                            var message = {
                                role: 'assistant',
                                content: data.response
                            };

                            this.messages.push(message);

                            this.sending_message = false;


                            // Reset textarea size
                            const textarea = this.$refs['message'];
                            textarea.style.height = 'initial';


                            // Scroll to the bottom of the messages container
                            this.$nextTick(() => {
                                const container = this.$refs.messagesContainer;
                                container.scrollTop = container.scrollHeight;
                            });

                            // Dynamically resize the container height when the textarea value changes
                            this.$nextTick(() => {
                                const container = this.$refs.messagesContainer;
                                container.style.height = `${container.scrollHeight}px`;
                            });
                        })
                        .catch(error => {
                            if (error) {

                                var error_message = {
                                    role: 'assistant',
                                    content: "An error occurred, please refresh the page!"
                                };

                                this.messages.push(error_message);

                                this.sending_message = false;

                            }
                    });
                }
        },

        deleteChat(){
            this.deleteting_chat = true;
            axios.post(this.route('delete_chat', this.chat.id))
            
            .then(() => { 
                
                this.messages = []; 
                
                this.messageForm.message = ''; 
                this.deleteting_chat = false;
                })
                .catch(error => {
                    
            });
        },

        resizeTextarea(){
            const textarea = this.$refs['message'];
            textarea.style.height = 'initial';
            textarea.style.height = `${textarea.scrollHeight}px`; 

            this.$nextTick(() => {
            const container = this.$refs.messagesContainer;
            container.scrollTop = container.scrollHeight;
            });

            // Dynamically resize the container height when the textarea value changes
            this.$nextTick(() => {
            const container = this.$refs.messagesContainer;
            container.style.height = `${container.scrollHeight}px`;
            });

        },

        storeContact(){
            this.$inertia.post(route('store_contact', this.chat.id), this.form , { 
                preserveState: false, 
                
            });

            this.chat_contact_sent = true;
            },
        },

    
    computed : {
        showContact(){
            return this.currently_selected_chatbot.show_contact_form == true && (this.currently_selected_chatbot.number_of_times_before_asking == this.all_messages.length/2) && this.chat.contacts_sent == false; 
        }
    },

    mounted() {
        // Scroll to the bottom of the messages container
        this.$nextTick(() => {
        const container = this.$refs.messagesContainer;
        container.scrollTop = container.scrollHeight;
        });

        // Dynamically resize the container height when the textarea value changes
        this.$nextTick(() => {
        const container = this.$refs.messagesContainer;
        container.style.height = `${container.scrollHeight}px`;
        });
    },
}
</script>

<style scoped>
    button:disabled{
        opacity:75%;
        cursor: not-allowed;
    }

    .dot-typing {
        position: relative;
        left: -9999px;
        width: 8px;
        height: 8px;
        border-radius: 5px;
        background-color: gray;
        color: gray;
        box-shadow: 9984px 0 0 0 gray, 9999px 0 0 0 gray,
            10014px 0 0 0 gray;
        animation: dot-typing 1.5s infinite linear;
    }

    @keyframes dot-typing {
        0% {
            box-shadow: 9984px 0 0 0 gray, 9999px 0 0 0 gray,
                10014px 0 0 0 gray;
        }
        16.667% {
            box-shadow: 9984px -10px 0 0 gray, 9999px 0 0 0 gray,
                10014px 0 0 0 gray;
        }
        33.333% {
            box-shadow: 9984px 0 0 0 gray, 9999px 0 0 0 gray,
                10014px 0 0 0 gray;
        }
        50% {
            box-shadow: 9984px 0 0 0 gray, 9999px -10px 0 0 gray,
                10014px 0 0 0 gray;
        }
        66.667% {
            box-shadow: 9984px 0 0 0 gray, 9999px 0 0 0 gray,
                10014px 0 0 0 gray;
        }
        83.333% {
            box-shadow: 9984px 0 0 0 gray, 9999px 0 0 0 gray,
                10014px -10px 0 0 gray;
        }
        100% {
            box-shadow: 9984px 0 0 0 gray, 9999px 0 0 0 gray,
                10014px 0 0 0 gray;
        }
    }
</style>