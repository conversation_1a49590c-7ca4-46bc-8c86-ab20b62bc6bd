<template> 
    <AppLayout title="Conversations">
        <div class="w-full h-full p-5 bg-gray-200 overflow-hidden"> 
            <div class="w-full h-full  flex items-center border border-gray-300 rounded-xl bg-white">
                <div class="h-full w-2/5 p-4 border-r border-gray-300 rounded-l-xl bg-white">
                    <div class="h-[5%] w-full text-lg font-semibold text-gray-500">All chats</div>

                    <div v-if="$page.props.conversations.length >0 && selectedChat " class="h-[86%] w-full mt-10 overflow-y-auto ">
                        <button 
                        v-for="(conversation, index) in $page.props.conversations"
                        :key="index"
                        @click="selectedChat = conversation"
                        :class="{'bg-gray-100 shadow-sm shadow-gray-300' : selectedChat && conversation.id == selectedChat.id}"
                        class="w-full px-4 py-3 ">
                            <div class="w-full  flex items-center justify-between">
                                <div class="flex items-center space-x-3">
                                    <div class="w-10 h-10 flex flex-col items-center justify-center rounded-lg bg-primaryColor">
                                        <icon name="message" class="w-4 text-white"></icon>
                                    </div>

                                    <h1 class="text-md text-primaryColor">Visitor {{ conversation.random_id.substring(0, 5) }}</h1>
                                </div>

                                <div class="text-xs text-gray-400"> 
                                     {{moment(conversation.created_at).format('D MMM h:mm ')}} 
                                </div>
                            </div> 

                            <p class="text-sm text-left mt-3 text-gray-600">
                                {{ ( Object.values((getRecentAssistantMessage(conversation.context)))[0] || '').toString().substring(0, 50) }} 
                            </p>
                        </button> 
                    </div>
                    
                    <div v-else class="h-[86%] w-full mt-10 flex flex-col items-center justify-center">No conversations</div>
                </div>

                <div class="h-full w-3/5 pt-6 pb-3 px-5 bg-gray-50 rounded-r-xl">
                    <div v-if="selectedChat != null" class="w-full h-[15%] flex items-center justify-between">
                        <h1 class="text-xl font-bold">Visitor  <span >{{ selectedChat.random_id.substring(0, 5) }}</span> </h1>

                        <button @click.prevent="deleteConversation" class="w-8 h-8 flex flex-col items-center justify-center rounded-md border border-gray-300 bg-white group">
                            <icon name="delete" class="text-red-400 w-4 group-hover:text-red-500"></icon>
                        </button>
                    </div>

                    <div class="w-full h-[85%] bg-white border px-5 py-4 border-gray-300 rounded-lg overflow-y-auto" ref="messagesContainer">
                        <div v-if="selectedChat && !JSON.parse(selectedChat.context).length" class="w-full h-full flex flex-col items-center justify-center">
                            No messages found !
                        </div>
                        <div v-else class="w-full">
                            <div 
                            v-if="selectedChat"
                            v-for="(message, index) in JSON.parse(selectedChat.context)"
                                :key="index"
                                class="w-full">   
                                
                                <div v-if="message.role == 'user'" class="w-full flex justify-end my-3">
                                    <div class="bg-primaryColor rounded-3xl p-3">
                                        <p class="text-xs text-white">{{ message.content }}</p>
                                    </div>
                                </div>
        
                                <div v-if="message.role == 'assistant'" class="w-full flex items-end space-x-3">
                                    <div  class="flex-shrink-0 bg-primaryColor rounded-full w-8 h-8 p-1.5 flex justify-center flex-col items-center">
                                        <img src="/img/bot.png"  alt="">
                                    </div>
        
                                    <div class="bg-gray-200 rounded-3xl p-4">
                                        <p class="text-xs text-gray-800">{{ Object.values(message.content)[0] }}</p>
                                    </div>
                                </div>
    
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>  
    </AppLayout>

    <ConfirmDialog></ConfirmDialog>

</template>

<script> 
import AppLayout from '@/Layouts/AppLayout.vue'; 
import Icon from '@/Components/Global/Icon.vue'    
import {  Link } from '@inertiajs/vue3';    
import moment from 'moment'; 
import 'primeicons/primeicons.css' 
import ConfirmDialog from 'primevue/confirmdialog';  

export default {
    components :{ 
        AppLayout,
        Icon,
        Link,  
        ConfirmDialog
    },

    props : {
        
    },

    data() {
        return {   
            moment: moment,  
            selectedChat : null,

        }
    },

    mounted(){
        this.selectedChat = this.$page.props.conversations[0];

        this.$nextTick(() => {
        const container = this.$refs.messagesContainer;
        container.scrollTop = container.scrollHeight;
                        });
    },

    methods: { 
        getRecentAssistantMessage(messages) {
            try {
                // Ensure messages are parsed and valid
                const parsedMessages = Array.isArray(messages)
                ? messages
                : JSON.parse(messages);

                // Filter assistant messages
                const assistantMessages = parsedMessages.filter(
                (msg) => msg.role === "assistant"
                );

                // Return the most recent assistant message content
                return assistantMessages.length > 0
                ? assistantMessages[assistantMessages.length - 1].content
                : "No assistant message found";
            } catch (error) {
                return "Error processing messages";
            }
        },

        deleteConversation() {
            this.$confirm.require({
                message: 'Do you want to delete this conversation?',
                header: 'Delete Conversation',
                icon: 'pi pi-info-circle',
                rejectProps: {
                    label: 'Cancel',
                    severity: 'secondary',
                    outlined: true
                },
                acceptProps: {
                    label: 'Delete',
                    severity: 'danger'
                },

                accept: () => {
                    this.$inertia.post(route('delete_faqs'), this.form , { 
                        preserveState: false, 
                    });
                },
                reject: () => {
                    
                }
            });
        }
   
    },
}
</script>