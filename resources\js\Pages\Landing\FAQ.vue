<template>
    <Head title="FAQs" />

    <Header></Header> 

    <section class="h-[200px] lg:h-[300px] flex flex-col items-center justify-center bg-gradient-to-r from-primaryColor to-black px-5 lg:px-0">
        <h1 class="text-2xl text-center md:text-3xl lg:text-5xl font-bold text-white">Frequently Asked Questions</h1>
    </section>

    <section class="w-full my-24 lg:my-24 px-6 lg:px-28">
 
        <div class="w-full mt-16 flex flex-col lg:flex-row lg:space-y-0 space-y-6 lg:items-start lg:space-x-8" >
            <div class="w-full lg:w-1/2 flex flex-col space-y-8 "  >
                <button v-for="(faq, index) in faqs1" :key="index" @click="faq.open = !faq.open " class="w-full shadow-md hover:cursor-pointer rounded-lg px-6 lg:px-10 py-6 lg:py-8  border border-gray-100">
                    <div class="flex items-center justify-between">
                        <div class="text-left flex-1 text-md lg:text-lg">
                            {{ faq.question }}
                        </div>
                        <div class="w-5 lg:w-8 flex justify-end">
                            <span v-if="faq.open == true">
                                <svg xmlns="http://www.w3.org/2000/svg"  fill="currentColor" class="bi bi-chevron-down w-4 text-gray-500" viewBox="0 0 16 16">
                                    <path fill-rule="evenodd" d="M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708"/>
                                </svg>
                            </span>

                            <span v-if="faq.open == false">
                                <svg xmlns="http://www.w3.org/2000/svg" fill="currentColor" class="bi bi-chevron-up w-4 text-gray-500" viewBox="0 0 16 16">
                                    <path fill-rule="evenodd" d="M7.646 4.646a.5.5 0 0 1 .708 0l6 6a.5.5 0 0 1-.708.708L8 5.707l-5.646 5.647a.5.5 0 0 1-.708-.708z"/>
                                </svg>
                            </span>
                        </div>
                    </div>

                    <div v-if="faq.open == true" class="w-full mt-4 ">
                        <p class="text-sm lg:text-md text-gray-500 text-left">{{faq.answer}}</p>
                    </div>
                </button>
            </div>
            
            <div class="w-full lg:w-1/2 flex flex-col space-y-8 mt-10 lg:mt-0"  >
                <button v-for="(faq, index) in faqs2" :key="index" @click="faq.open = !faq.open " class="w-full shadow-md hover:cursor-pointer rounded-lg px-6 lg:px-10 py-6 lg:py-8  border border-gray-100">
                    <div class="flex items-center justify-between">
                        <div class="text-left flex-1 text-sm lg:text-lg">
                            {{ faq.question }}
                        </div>
                        <div class="w-5 lg:w-8 flex justify-end">
                            <span v-if="faq.open == true">
                                <svg xmlns="http://www.w3.org/2000/svg"  fill="currentColor" class="bi bi-chevron-down w-4 text-gray-500" viewBox="0 0 16 16">
                                    <path fill-rule="evenodd" d="M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708"/>
                                </svg>
                            </span>

                            <span v-if="faq.open == false">
                                <svg xmlns="http://www.w3.org/2000/svg" fill="currentColor" class="bi bi-chevron-up w-4 text-gray-500" viewBox="0 0 16 16">
                                    <path fill-rule="evenodd" d="M7.646 4.646a.5.5 0 0 1 .708 0l6 6a.5.5 0 0 1-.708.708L8 5.707l-5.646 5.647a.5.5 0 0 1-.708-.708z"/>
                                </svg>
                            </span>
                        </div>
                    </div>

                    <div v-if="faq.open == true" class="w-full mt-4 ">
                        <p class="text-sm lg:text-md text-gray-500 text-left">
                            {{ faq.answer }}
                        </p>
                    </div>
                </button>
            </div> 
        </div>
    </section>
 
    <section class="w-full flex justify-center bg-primaryColor/20 mt-20 -mb-20">
        <div class="w-full px-6 lg:w-3/5 justify-center flex flex-col items-center pt-16  md:pt-20 lg:pt-24 pb-32 ">
            <h1 class="text-2xl lg:text-3xl text-gray-600 text-center">Start using Botomatic Now!</h1>

            <p class="mt-5 text-gray-500 text-md text-center">Set up in minutes. No credit card required.</p>

            <div class="mt-8 mb-10 flex flex-col space-y-2 lg:space-y-0 md:flex-row items-center md:space-x-2">
                <Link :href="route('register')" class="bg-primaryColor text-white text-sm md:text-md font-bold px-8 py-2 md:py-3 border-2 border-primaryColor hover:bg-transparent hover:text-primaryColor rounded-md">Register Now!</Link>

                <Link :href="route('login')" class="border-2 text-sm md:text-md border-primaryColor text-primaryColor hover:bg-primaryColor px-8 py-2 md:py-3 rounded-md hover:text-white">Login</Link>
            </div>
        </div>
    </section>  
    
    <Footer></Footer>
</template>
 

<script>
import { Head, Link } from '@inertiajs/vue3';
import Icon from '@/Components/Global/Icon.vue';
import Footer from '@/Components/Landing/Footer.vue';
import Header from '@/Components/Landing/Header.vue';

export default {
    components :{
        Head, Link,
        Icon,
        Footer,
        Header,
    },

 data(){
        return { 
            faqs1: [
                    {
                        open: true,
                        question: 'What is Botomatic?',
                        answer: 'Botomatic is an AI-powered platform that lets you upload your business data and instantly create a chatbot to handle customer questions on your website or app.',
                    },
                    {
                        open: false,
                        question: 'How does Botomatic work?',
                        answer: 'Simply upload your documents, FAQs, or resources, and Botomatic will use that data to train a bot that can provide accurate answers to users in real-time.',
                    },
                    {
                        open: false,
                        question: 'Can I customize my bot’s responses?',
                        answer: 'Yes! You can fine-tune your bot’s responses, define its tone, and even add custom fallback messages to ensure a personalized experience.',
                    },
            ],

            faqs2: [
                    {
                        open: false,
                        question: 'Is Botomatic suitable for small businesses?',
                        answer: 'Absolutely! Botomatic is designed to scale, making it perfect for startups, small businesses, and large enterprises alike.',
                    },
                    {
                        open: false,
                        question: 'What platforms can I deploy my bot on?',
                        answer: 'You can deploy your bot on websites, social media platforms, and messaging apps, or even use it internally for team support.',
                    },
                    {
                        open: false,
                        question: 'Is my data secure with Botomatic?',
                        answer: 'Yes! We use advanced encryption and security measures to protect your data and ensure your bots only access what you allow.',
                    },
            ]
        }
    },
 
}

</script>