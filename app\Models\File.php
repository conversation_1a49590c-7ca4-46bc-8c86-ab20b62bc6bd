<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class File extends Model
{
    use HasFactory;

    protected $fillable = [ 
        'model',
        'file_name',
        'mime_type',
        'file_size',
        'user_id', 
    ];    

    public function model():MorphTo
    {
        return $this->morphTo();
    }

    public function getPreviewUrlAttribute()
    {        
        $local_storage_url = url("storage/user_file/".auth()->user()->email."/{$this->created_at->format('Y/m/d')}/{$this->id}/{$this->file_name}");

        $urls = collect([
            'image' => [
                'mimes' => [
                    'image/gif', 
                    'image/avif', 
                    'image/apng', 
                    'image/png', 
                    'image/svg+xml', 
                    'image/webp', 
                    'image/jpeg', 
                ],
            'preview_url' => $local_storage_url
            ],

            'document' => [
                'mimes' => [
                    'application/msword',
                    'application/pdf'
                ],
                'preview_url' => asset('img/mime_types/download.png')
            ],

        ]);

        $fileType = $urls->first(function ($item) {
            return in_array($this->mime_type, $item['mimes']);
        });

        return $fileType['preview_url'] ?? asset("img/mime_types/other_document.svg");
    }
}
