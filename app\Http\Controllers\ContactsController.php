<?php

namespace App\Http\Controllers;

use App\Models\Chat;
use Inertia\Inertia;
use App\Models\ChatContact;
use Illuminate\Http\Request;

class ContactsController extends Controller
{
    public function contacts() {
 
        $user_chats = Chat::where('user_id', auth()->user()->id)->with('contacts')->get();

        $contacts = $user_chats->pluck('contacts')->flatten();

        return Inertia::render('Contacts/Contacts', [ 
            'chatbots' => $this->chatbots(),
            'currently_selected_chatbot' => $this->selectedBot(),
            'contacts' => $contacts,
        ]);
         
    }


    public function store_contact(Request $request , $chat_id) {
        ChatContact::create([
            'name' => $request->name,
            'email' => $request->email,
            'session_id' => request()->getSession()->getId(),
            'ip' =>  request()->ip(),
            'agent' => request()->header('User-Agent'),
            'chat_id' => $chat_id,
        ]);

        $chat = Chat::where('id', $chat_id)->first();

        $chat->forceFill([
            'contacts_sent' => true,
        ])->save();

        return redirect()->back();

    }

    public function delete_contact($id) { 

        ChatContact::destroy($id);

        return redirect()->back();
    }
}
 