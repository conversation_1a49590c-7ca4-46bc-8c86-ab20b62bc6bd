<template>

<button @click="setIsOpen(true)" class="p-1 hover:bg-opacity-75 rounded-full z-50"> 
    <button class="relative w-20 text-sm rounded-full border-2 border-primaryColor text-primaryColor font-semibold"><span class="text-green-600">{{auth_user.onboarding_step}}</span> / 2
    
        <span  class="flex absolute -top-0.5 -right-0.5 w-2 h-2 "> 
            <span class="animate-ping absolute inline-flex h-full w-full rounded-full bg-red-400 opacity-75"></span>
            <span class="relative inline-flex rounded-full h-2 w-2 bg-red-500"></span> 
        </span>
    </button>
</button>

<TransitionRoot appear as="template" :show="isOpen">
    <Dialog 
    
    class=" fixed inset-0  left-0 overflow-hidden z-50"
    :open="isOpen" @close="setIsOpen">

        <TransitionChild 
            enter="transition-opacity ease-in-out duration-300"
            enter-from="opacity-0"
            enter-to="opacity-100"
            leave="transition-opacity ease-in-out duration-300"
            leave-from="opacity-100"
            leave-to="opacity-0"
            as="template">

            <DialogOverlay class="absolute inset-0  bg-black bg-opacity-40 " />
        </TransitionChild>

        <TransitionChild 
            enter="transform ease-in-out transition-transform duration-300"
            
            leave="transform ease-in-out transition-transform duration-300"
            leave-from="translate-x-full"
            leave-to="translate-x-0"
            as="template"> 
            
            <div class="bg-white fixed inset-y-0 left-0 h-full w-full max-w-64">  

                <div class="overflow-y-scroll h-full px-6 pt-8 ">
                    <DialogDescription class="overflow-y-scroll h-full ">
                         
                        <div class="w-full">
                            <div class="mt-6 w-full flex justify-left">
                                <img src="/img/logos/logo.webp"  class="h-10" />
                            </div> 
                
                            <div class="w-full mt-10">
                                <div 
                                v-for="(step, index) in onboarding_steps" 
                                :key="index"
                                class="flex items-center mb-6 space-x-2"> 
                                    <div 
                                    :class="{'border-none bg-green-500' : auth_user.onboarding_step > index + 1}" 
                                        class="font-semibold bg-gray-200 text-sm text-primaryColor w-6 h-6 border border-primaryColor rounded-full flex flex-col items-center justify-center">
                                        
                                        <icon v-if="auth_user.onboarding_step > index + 1" name="check" class="w-4 text-white"></icon>

                                        <span v-else class="">
                                            {{index+1}}
                                        </span>
                                    </div>
                                    <span  
                                    v-if="auth_user.onboarding_step > index + 1"
                                    class="text-sm font-medium text-gray-400/80">
                                    {{step.name}}
                                    </span>

                                    <span  
                                    v-else
                                    class="text-sm font-medium text-primaryColor">
                                    {{step.name}}
                                    </span>
                                </div>
                            </div>
                        </div>

                    </DialogDescription>    
                </div>
 

            </div>
        </TransitionChild>

        
    </Dialog>
</TransitionRoot>

</template>

<script>
  import { ref } from "vue";
  import {
    Dialog,
    DialogOverlay,
    DialogTitle,
    DialogDescription,
    TransitionRoot,
    TransitionChild
  } from "@headlessui/vue";
    import Icon from '@/Components/Global/Icon.vue'  
    import { Link } from '@inertiajs/vue3'

  export default {
    components: { 
        Icon,
        Link,
        TransitionRoot,
        TransitionChild, 
        Dialog, DialogOverlay, 
        DialogTitle, 
        DialogDescription 
    },

    data() {
        return { 
            auth_user : this.$page.props.auth.user,

            onboarding_steps : [ 
                    {
                        id : 1 ,
                        name : 'Personal Info',
                        completion_status : false,
                    },
                    {
                        id : 2 ,
                        name : 'KYC Documents',
                        completion_status : false,
                    },
                    {
                        id : 3 ,
                        name : 'Payment & Verification',
                        completion_status : false,
                    },
                    
                    
                ],
                
        }
    },

    setup() {
      let isOpen = ref(false);

      return {
        isOpen,
        setIsOpen(value) {
          isOpen.value = value;
        }, 
      };
    },

 
  };
</script>