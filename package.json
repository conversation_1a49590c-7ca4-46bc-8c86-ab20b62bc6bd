{"private": true, "type": "module", "scripts": {"dev": "vite", "build": "vite build"}, "devDependencies": {"@inertiajs/vue3": "^1.0.0", "@tailwindcss/forms": "^0.5.2", "@tailwindcss/typography": "^0.5.2", "@vitejs/plugin-vue": "^4.5.0", "autoprefixer": "^10.4.7", "axios": "^1.6.4", "laravel-vite-plugin": "^1.0.0", "postcss": "^8.4.14", "tailwind-scrollbar": "^3.1.0", "tailwindcss": "^3.1.0", "vite": "^5.0.0", "vue": "^3.2.31"}, "dependencies": {"@cyhnkckali/vue3-color-picker": "^2.0.4", "@headlessui/vue": "^1.7.23", "@primevue/themes": "^4.2.2", "moment": "^2.30.1", "primeicons": "^7.0.0", "primevue": "^4.2.2", "vue-country-flag-next": "^2.3.2", "vue-file-agent": "^1.7.3", "vue-paypal-checkout": "^3.2.0"}}