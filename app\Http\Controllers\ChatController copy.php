<?php

namespace App\Http\Controllers;

use App\Models\Chat;
use Inertia\Inertia;
use App\Models\AiFaq;
use App\Models\Chatbot;
use Illuminate\Support\Str;
use Illuminate\Http\Request;
use App\Services\VectorService;
use OpenAI\Laravel\Facades\OpenAI;
use Illuminate\Support\Facades\View;

class ChatController extends Controller
{
    public function send_message_in_chat(Request $request, $chat_id)
    {
        $input = $request->message;

        // Fetch chat and chatbot details
        $defaultchat = Chat::where('id', $chat_id)->first();

        $selected_chatbot = Chatbot::where('id', $defaultchat->chatbot_id)->first();

        // Retrieve and decode existing chat context
        $messages_no_ai = json_decode($defaultchat->context, true) ?? [];
        $messages = $messages_no_ai;

        try {
            // Convert input into vector
            $vector = OpenAI::embeddings()->create([
                'model' => 'text-embedding-ada-002',
                'input' => $input,
            ]);

            // Instantiate VectorService and fetch relevant chunks
            $vectorService = new VectorService();
            
            $relevantChunks = $vectorService->getMostSimilarVectors(
                $vector['data'][0]['embedding'],
                $selected_chatbot->id,
                4 // Limit relevant chunks to 4
            );

            $similarTexts = $vectorService->getTextsFromIds(array_column($relevantChunks, 'id'));
            $knowledgeBase = implode(' ', array_slice($similarTexts, 0, 3)); // Combine top 3 texts

            // Add user input to messages
            $messages[] = ['role' => 'user', 'content' => $input];
            $messages_no_ai[] = ['role' => 'user', 'content' => $input];

            $faqs = AiFaq::where('chatbot_id', $chat_id)->get();

            // Initialize the system message
            $systemMessage = "You are an expert in answering questions. Use relevant information provided but maintain a conversational tone. Avoid redundant or verbose replies. Be able to hold a simple conversation, respond to greetings, and things like that. Use the following Frequently Asked Questions and Answers where nercessary: \n\n";
            
            if ( $faqs) {  
                foreach ($faqs as $faq) {
                    $systemMessage .= "Q: " . $faq->question . "\n";
                    $systemMessage .= "A: " . $faq->answer . "\n\n";
                }
            }

            $messages[] = ['role' => 'system', 'content' => $systemMessage];

            // Add concise prompt with knowledge base
            $prompt = "Answer the following question using the context provided : " .$input . '.Context:' . $knowledgeBase . "Be short , be concise and answer what is being asked. And also be able to handle a simple conversation like greetings without putting unnecersary info.";
            $messages[] = ['role' => 'system', 'content' => $prompt];

            // Ensure there is always a minimum valid message structure
            if (empty($messages)) {
                throw new \Exception('Message array is empty, ensure at least one user message is present.');
            }

            // Estimate tokens and truncate if necessary
            $messages = $this->truncateMessages($messages, 15000); // Ensure a safe limit below 16,385

            // Ensure there are still messages after truncation
            if (empty($messages)) {
                $messages[] = ['role' => 'system', 'content' => $systemMessage];
                $messages[] = ['role' => 'user', 'content' => $input];
            }

            // Generate AI response
            $response = OpenAI::chat()->create([
                'model' => 'gpt-3.5-turbo',
                'messages' => $messages,
            ]);

            // Append assistant's response to messages
            $messages[] = ['role' => 'assistant', 'content' => $response->choices[0]->message->content];
            
            $messages_no_ai[] = ['role' => 'assistant', 'content' => $response->choices[0]->message->content];

            // Save updated context
            $defaultchat->forceFill([
                'context' => json_encode($messages_no_ai),
            ])->save();

            return ['response' => $response];

        } catch (\Throwable $th) {
            return response()->json(['error' => $th->getMessage()], 500);
        }
    }

    /**
     * Truncate messages to fit within a token limit.
     *
     * @param array $messages
     * @param int $maxTokens
     * @return array
     */
    private function truncateMessages(array $messages, int $maxTokens): array
    {
        $currentTokens = 0;
        $truncatedMessages = [];

        foreach (array_reverse($messages) as $message) {
            $messageTokens = ceil(mb_strlen($message['content']) / 4); // Approximate token count
            if ($currentTokens + $messageTokens > $maxTokens) {
                break;
            }
            $currentTokens += $messageTokens;
            array_unshift($truncatedMessages, $message); // Add back in correct order
        }

        // Always ensure at least the latest user message and system instructions are kept
        if (empty($truncatedMessages)) {
            $truncatedMessages = array_slice($messages, -2); // Keep last two messages
        }

        return $truncatedMessages;
    }

    public function delete_chat($id)
    {
        try {
            $chat = Chat::findOrFail($id);
            $chat->forceFill(['context' => json_encode([])])->save();

            return response()->json(['message' => 'Chat context cleared.']);
        } catch (\Throwable $th) {
            \Log::error('Error in delete_chat: ' . $th->getMessage());
            return response()->json(['error' => 'Failed to delete chat.'], 500);
        }
    }

    public function start_contact(Request $request) {
        $chatbot = Chatbot::findOrFail($request->chatbot_id);
    
        $chat = Chat::create([
            'chatbot_id' => $chatbot->id,
            'user_id' => $chatbot->user_id,
            'random_id' => Str::uuid(),
            'context' => $chatbot->welcome_message,
            'context_added' => false,
            'session_id' => session()->getId(),
            'ip' => $request->ip(),
            'agent' => $request->header('User-Agent'),
            'test_chat' => false,
        ]);
    
        return response()->json(['chat_id' => $chat->id, 'message' => $chatbot->welcome_message]);
    }

    public function getSettings($chatbot_id)
    {
        $chatbot = Chatbot::where('random_id', $chatbot_id)->first();

        if (!$chatbot) {
            return response()->json(['error' => 'Chatbot not found'], 404);
        }

        return response()->json($chatbot);
    }

    public function chatbotIframe($chatbot_id)
    {
        $chatbot = Chatbot::where('random_id', $chatbot_id)->first();

        if (!$chatbot) {
            return response()->json(['error' => 'Chatbot not found'], 404);
        }

        $chat_instance = Chat::where('ip', request()->ip())->where('agent', request()->header('User-Agent'))->first();

        if (!$chat_instance) {
            $chat_instance = Chat::create([
                'chatbot_id' => $chatbot->id, 
                'random_id' => $this->randomNumber(),  
                'context' => json_encode([]),  
                'session_id' => null,
                'ip' =>  request()->ip(),
                'agent' => request()->header('User-Agent'),
                'test_chat' => true, 
                'context_added' => true, 
            ]); 
        }

        $all_messages = null;

        if ($chat_instance->context != []) {
            $all_messages = json_decode($chat_instance->context); 
        }

        return Inertia::render('TryMyAi/MyAiBotIframe', [ 
            'chat' => $chat_instance, 
            'all_messages' => $all_messages, 
            'currently_selected_chatbot' => $chatbot, 
        ]); 

    }


}
