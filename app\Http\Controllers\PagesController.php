<?php

namespace App\Http\Controllers;

use App\Models\Post;
use Inertia\Inertia;
use Illuminate\Http\Request;

class PagesController extends Controller
{
    public function index() {
  
        $posts = Post::take(3)->get();

        return Inertia::render('Landing/Welcome', [ 
           'posts' => $posts
        ]);
    }

    public function about() {
  
        return Inertia::render('Landing/About', [ 
           
        ]);
    }

    public function blog() {
        $posts = Post::take(3)->get();
  
        return Inertia::render('Landing/Blog', [ 
            'posts' => $posts
        ]);
    }

    public function view_blog($slug) {
        
        $post = Post::where('slug', $slug)->first();

        $other_posts = Post::where('slug','!=', $slug)->get()->take(3);

        return Inertia::render('Landing/ViewBlog', [ 
           'post' => $post,
           'other_posts' => $other_posts,
        ]);
    }

    public function faqs() {
  
        return Inertia::render('Landing/FAQ', [ 
           
        ]);
    }
    
    public function contact() {
  
        return Inertia::render('Landing/Contact', [ 
           
        ]);
    }
    
}
