<template>
    <div class="h-full hidden lg:flex w-full lg:w-[25%] bg-gray-200 lg:p-10 p-5">
        <div class="w-full">
            <div class="flex items-center space-x-2">
                <img src="/img/logos/logo.webp" class="h-12" /> 
                <span class="text-xl font-workSans font-semibold">Seedlyy</span>
            </div>
 
            <div class="w-full mt-16">
                <div 
                v-for="(step, index) in onboarding_steps" 
                :key="index"
                class="flex items-center mb-6 space-x-2"> 
                    <div 
                    :class="{'border-none bg-green-500' : auth_user.onboarding_step > index + 1}" 
                        class="font-semibold bg-gray-200 text-sm text-primaryColor w-6 h-6 border border-primaryColor rounded-full flex flex-col items-center justify-center">
                        
                        <icon v-if="auth_user.onboarding_step > index + 1" name="check" class="w-4 text-white"></icon>

                        <span v-else class="">
                            {{index+1}}
                        </span>
                    </div>
                    <span  
                    v-if="auth_user.onboarding_step > index + 1"
                    class="text-sm font-medium text-gray-400/80">
                    {{step.name}}
                    </span>

                    <span  
                    v-else
                    class="text-sm font-medium text-primaryColor">
                    {{step.name}}
                    </span>
                </div>
            </div>
        </div>
    </div>
</template>

<script>

    import Icon from '@/Components/Global/Icon.vue';

    export default {

        components : {
            Icon
        },

        data(){
            return {
                auth_user : this.$page.props.auth.user,
                onboarding_steps : [
                    {
                        id : 1 ,
                        name : 'Personal Info',
                        completion_status : false,
                    },
                    {
                        id : 2 ,
                        name : 'KYC Documents',
                        completion_status : false,
                    },
                    {
                        id : 3 ,
                        name : 'Payment & Verification',
                        completion_status : false,
                    },
                    
                ],
            }
        },

        computed: {

        }
    }
 
</script>