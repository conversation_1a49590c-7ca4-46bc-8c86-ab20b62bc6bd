<template>
    <Head title="Blog" />

    <Header></Header>

 
    <section class="h-[200px] lg:h-[300px] flex flex-col items-center justify-center bg-gradient-to-r from-primaryColor to-black px-5 lg:px-0">
        <h1 class="text-2xl text-center md:text-3xl lg:text-5xl font-bold text-white">Blog</h1>
    </section>

     <section class="w-full mt-16 px-2 lg:px-12">
        <h1 class="text-xl lg:text-2xl font-bold text-gray-800 pl-6">Recent Blog Posts</h1>

        <div class="w-full my-10 flex flex-wrap">
            <Link v-for="(post, index) in $page.props.posts" :key="index" :href="route('view_blog', post.slug)" class="w-full lg:w-1/3 p-6 group mb-6">
                <div class="w-full">
                    <div class="w-full h-52 rounded-lg">
                        <img class="w-full h-full object-cover object-center rounded-lg"  :src="'/storage/'+ post.image" alt="">
                    </div>

                    <div class="mt-4">
                        <h4 class="text-sm md:text-md text-gray-600">{{ new Date(post.created_at).toLocaleDateString('en-US', { year: 'numeric', month: 'short', day: 'numeric' }) }}</h4>

                        <h1 class="text-md md:text-lg mt-3 text-gray-500 font-bold group-hover:text-primaryColor">{{post.title}}</h1>

                        <p class="text-sm md:text-md text-gray-400 mt-3">{{post.excerpt}}</p>

                        <div class="w-full flex justify-between mt-3">
                            <div class="flex items-center space-x-2">
                                <img src="/img/logos/logo.webp" class="w-5 h-5 rounded-full" alt="">
                                <span class="text-sm md:text-md text-gray-500">Head of content</span>
                            </div>

                            <div class="flex items-center space-x-2">
                                <icon name="duration" class="w-3 text-gray-400"></icon>
                                <span class="text-xs md:text-sm text-gray-400">{{post.duration}}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </Link> 
        </div>
     </section>

     <section class="w-full flex justify-center bg-primaryColor/20 mt-20 -mb-20">
        <div class="w-full px-6 lg:w-3/5 justify-center flex flex-col items-center pt-16  md:pt-20 lg:pt-24 pb-32 ">
            <h1 class="text-2xl lg:text-3xl text-gray-600 text-center">Start using Botomatic Now!</h1>

            <p class="mt-5 text-gray-500 text-md text-center">Set up in minutes. No credit card required.</p>

            <div class="mt-8 mb-10 flex flex-col space-y-2 lg:space-y-0 md:flex-row items-center md:space-x-2">
                <Link :href="route('register')" class="bg-primaryColor text-white text-sm md:text-md font-bold px-8 py-2 md:py-3 border-2 border-primaryColor hover:bg-transparent hover:text-primaryColor rounded-md">Register Now!</Link>

                <Link :href="route('login')" class="border-2 text-sm md:text-md border-primaryColor text-primaryColor hover:bg-primaryColor px-8 py-2 md:py-3 rounded-md hover:text-white">Login</Link>
            </div>
        </div>
    </section>  
    <Footer></Footer>

</template>
 

<script>
import { Head, Link } from '@inertiajs/vue3';
import Icon from '@/Components/Global/Icon.vue';
import Footer from '@/Components/Landing/Footer.vue';
import Header from '@/Components/Landing/Header.vue';

export default {
    components :{
        Head, Link,
        Icon,
        Footer,
        Header
    },

    data(){
        return { 
            faqs1: [
                {
                    open: true,
                    question: 'What is Skulbot?',
                    answer: 'Skulbot is an AI-powered platform designed to help students and teachers streamline learning and teaching with automation and smart tools.',
                },
                {
                    open: false,
                    question: 'How does Skulbot help teachers?',
                    answer: 'Skulbot generates lesson plans, quizzes, and summaries, saving teachers hours of preparation time while improving student engagement.',
                },
                {
                    open: false,
                    question: 'Can students use Skulbot for exam preparation?',
                    answer: 'Yes! Skulbot provides AI-generated quizzes, study summaries, and personalized learning recommendations to help students prepare effectively.',
                },
                {
                    open: false,
                    question: 'Is Skulbot free to use?',
                    answer: 'Skulbot offers a free plan with essential features, and premium plans with advanced AI tools for enhanced learning and teaching experiences.',
                },
                {
                    open: false,
                    question: 'Does Skulbot support multiple subjects?',
                    answer: 'Yes, Skulbot works across various subjects, helping students and teachers in different fields with AI-powered content generation and assistance.',
                },
            ],

            faqs2: [
                {
                    open: false,
                    question: 'How do I sign up for Skulbot?',
                    answer: 'Simply visit our website, click on "Sign Up," and follow the easy steps to create your account.',
                },
                {
                    open: false,
                    question: 'Can I use Skulbot on my phone?',
                    answer: 'Yes! Skulbot is accessible on both desktop and mobile devices, making it easy to study and teach on the go.',
                },
                {
                    open: false,
                    question: 'Does Skulbot work with Google Classroom?',
                    answer: 'Yes, Skulbot integrates with Google Classroom and other learning management systems to enhance your teaching experience.',
                },
                {
                    open: false,
                    question: 'Is my data safe with Skulbot?',
                    answer: 'Absolutely! Skulbot follows strict security protocols to keep your data safe and private.',
                },
                {
                    open: false,
                    question: 'How do I contact support?',
                    answer: 'You can reach our support team through the "Help" section on our website or email us for assistance.',
                },
            ],
             
        }
    },
 
}

</script>