<?php

namespace App\Http\Controllers;

use App\Models\Chatbot;
use App\Models\Business;
use Illuminate\Routing\Controller as BaseController;
use Illuminate\Foundation\Validation\ValidatesRequests;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;

class Controller extends BaseController
{
    use AuthorizesRequests, ValidatesRequests;

    public function selectedBot()
    {
        $chatbot = Chatbot::where('id', auth()->user()->currently_selected_chatbot)->first();

        

        return $chatbot;
    }
    
    public function chatbots()
    {
        $chatbots = Chatbot::where('user_id', auth()->user()->id)->get(); 


        return $chatbots;
    }

    public function randomNumber()
    {
        $random_number = '';
    
        for($i = 0; $i < 20; $i++) {
            $random_number .= mt_rand(0, 9);
        }

        return $random_number;
    }
    
}
