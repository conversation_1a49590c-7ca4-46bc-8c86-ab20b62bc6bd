<?php

use App\Models\User;
use Inertia\Inertia;
use App\Http\Middleware\Onboarding;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Artisan;
use App\Http\Controllers\ChatController;
use Illuminate\Foundation\Application;  
use App\Http\Controllers\LeadsController;
use App\Http\Controllers\MediaController;
use App\Http\Controllers\PagesController;
use App\Http\Controllers\TryMyAiController;
use App\Http\Controllers\ContactsController;
use App\Http\Controllers\DocumentsController;
use App\Http\Controllers\AppearanceController;
use App\Http\Controllers\DashboardController; 
use App\Http\Controllers\FaqResponsesController;
use App\Http\Controllers\TrainAiModelController;
use App\Http\Controllers\BusinessGoalsController;
use App\Http\Controllers\ConversationsController;
use App\Http\Controllers\CreateChatbotController;
use App\Http\Controllers\OnboardingController;   
use App\Http\Controllers\CustomizeChatbotController;
use App\Http\Controllers\TrainingMaterialsController;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

// Route::get('/run-terminal-command', function() {    
//     Artisan::call('route:cache'); 
//     Artisan::call('config:cache'); 
//     Artisan::call('cache:clear');  
//   return 'DONE'; 
// })->name('indexx'); 
 
Route::get('/', function () {
    if (Auth::check()) {
        $user = User::find(Auth::user()->id);

        if ($user) {
            return redirect()->route('dashboard');
        }
    }

    return redirect()->route('login');
})->name('index');

 
Route::post('/store-contact/{id}', [ContactsController::class, 'store_contact'])->name('store_contact');       

Route::post('/start-bot-chat', [ChatController::class, 'start_contact'])->name('start_contact');

Route::post('/send-message/{chat_id}', [ChatController::class, 'send_message_in_chat'])->name('send_message_in_chat');

Route::post('/delete-chat/{chat_id}', [ChatController::class, 'delete_chat'])->name('delete_chat');  

Route::middleware([ 
    'auth:sanctum',
    config('jetstream.auth_session'),
    'verified', 
    ])->group(function () {
        
    Route::post('/change-onboarding-step/{current_step}/{operation}', [OnboardingController::class, 'change_onboarding_step'])->name('change_onboarding_step'); 

    Route::post('/get-links', [CreateChatbotController::class, 'get_url_links'])->name('get_url_links');

    Route::post('/store-file', [DocumentsController::class, 'store_user_file'])->name('store_user_file'); 

    Route::post('/submit-chatbot-training-documents', [TrainAiModelController::class, 'submit_training_documents'])->name('submit_training_documents'); 

    Route::post('/submit-chatbot-training-links', [TrainAiModelController::class, 'submit_training_links'])->name('submit_training_links'); 

    Route::post('/customization-chatbot', [CustomizeChatbotController::class, 'customize_chatbot'])->name('customize_chatbot'); 

    // Media routes
    Route::post('/media', [MediaController::class, 'store'])->name('media.store');

    Route::delete('/media/delete/{media}', [MediaController::class, 'destroy'])->name('media.destroy');
});

Route::middleware([
    'auth:sanctum',
    config('jetstream.auth_session'),
    'verified',Onboarding::class,
])->group(function () {


    Route::get('/dashboard', [DashboardController::class, 'dashboard'])->name('dashboard');

    Route::get('/appearance', [AppearanceController::class, 'appearance'])->name('appearance');

    Route::post('/save-appearance-settings', [AppearanceController::class, 'save_appearance_settings'])->name('save_appearance_settings');
    
    Route::get('/demo', [TryMyAiController::class, 'demo'])->name('demo');
    
    Route::get('/training-materials', [TrainingMaterialsController::class, 'training_materials'])->name('training_materials');

    Route::post('/delete-training-materials', [TrainingMaterialsController::class, 'delete_training_material'])->name('delete_training_material');
    
    Route::get('/faq-responses', [FaqResponsesController::class, 'faq_responses'])->name('faq_responses');

    Route::post('/delete-faq', [FaqResponsesController::class, 'delete_faqs'])->name('delete_faqs');

    Route::post('/submit-faq', [FaqResponsesController::class, 'submit_faq'])->name('submit_faq');
    
    Route::get('/business-goals', [BusinessGoalsController::class, 'business_goals'])->name('business_goals');
    
    Route::get('/contacts', [ContactsController::class, 'contacts'])->name('contacts');
    
    Route::delete('/delete-contact/{contact_id}', [ContactsController::class, 'delete_contact'])->name('delete_contact');
    
    Route::get('/leads', [LeadsController::class, 'leads'])->name('leads');
        
    Route::post('/delete-chat/{chat_id}', [ChatController::class, 'delete_chat'])->name('delete_chat');      
    
    Route::get('/conversations', [ConversationsController::class, 'conversations'])->name('conversations');
});
