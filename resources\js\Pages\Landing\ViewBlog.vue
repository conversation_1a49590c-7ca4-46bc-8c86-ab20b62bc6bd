<template>
    <Head :title="'View Blog - ' + $page.props.post.title " />

    <Header></Header>

 
    <section class="w-full mt-20">
        <div class="w-full flex justify-center">
            <div class="w-3/5 flex flex-col items-center">
                <h1 class="text-3xl lg:text-5xl font-bold md:font-extrabold text-gray-700 text-center">
                    {{$page.props.post.title}}
                </h1>

                <p class="text-lg md:text-xl text-gray-600 text-center mt-8">
                    {{$page.props.post.excerpt}}
                </p>

                <p class="text-sm md:text-md text-gray-400 mt-8">{{ new Date($page.props.post.created_at).toLocaleDateString('en-US', { year: 'numeric', month: 'short', day: 'numeric' }) }}
                </p>
            </div>
        </div>

        <div class="w-full px-4 lg:px-12 mt-20">
            <img :src="'/storage/'+ $page.props.post.image" class="w-full rounded-xl" alt="">
        </div>

        <div class="w-full mt-10 flex flex-col lg:flex-row items-center lg:items-start lg:space-x-12 lg:pl-20">
            <div class="w-full lg:w-[70%] px-5">
                <p class="text-md md:text-lg text-gray-700" v-html="$page.props.post.body"></p>
            </div>

            <div class="w-full lg:w-[30%] px-5 lg:pr-10 mt-12 lg:mt-0">
                <h1 class="text-xl text-gray-600 mb-8">Read Also</h1>

                <div class="flex flex-col">
                    <Link 
                    v-for="(post, index) in $page.props.other_posts" 
                    :key="index"
                    :href="route('view_blog', post.slug)"
                    class="w-full flex items-start space-x-3 mb-6">
                        <div class="w-20 md:w-28 h-20 md:h-28 flex-shrink-0">
                            <img class="w-full h-full object-cover object-center rounded-lg" :src="'/storage/'+ post.image" alt="">
                        </div>
    
                        <div class="flex flex-col items-start">
                            <h1 class="text-md md:text-lg font-medium text-gray-500">{{post.title}}</h1> 
                        </div>
                    </Link>
                </div>

            </div>

        </div>
    </section>

    <section class="w-full flex justify-center bg-primaryColor/20 mt-20 -mb-20">
        <div class="w-full px-6 lg:w-3/5 justify-center flex flex-col items-center pt-16  md:pt-20 lg:pt-24 pb-32 ">
            <h1 class="text-2xl lg:text-3xl text-gray-600 text-center">Start using Botomatic Now!</h1>

            <p class="mt-5 text-gray-500 text-md text-center">Set up in minutes. No credit card required.</p>

            <div class="mt-8 mb-10 flex flex-col space-y-2 lg:space-y-0 md:flex-row items-center md:space-x-2">
                <Link :href="route('register')" class="bg-primaryColor text-white text-sm md:text-md font-bold px-8 py-2 md:py-3 border-2 border-primaryColor hover:bg-transparent hover:text-primaryColor rounded-md">Register Now!</Link>

                <Link :href="route('login')" class="border-2 text-sm md:text-md border-primaryColor text-primaryColor hover:bg-primaryColor px-8 py-2 md:py-3 rounded-md hover:text-white">Login</Link>
            </div>
        </div>
    </section> 
    <Footer></Footer>

</template>
 

<script>
import { Head, Link } from '@inertiajs/vue3';
import Icon from '@/Components/Global/Icon.vue';
import Footer from '@/Components/Landing/Footer.vue';
import Header from '@/Components/Landing/Header.vue';

export default {
    components :{
        Head, Link,
        Icon,
        Footer,
        Header
    },

    data(){
        return { 
            faqs1: [
                {
                    open: true,
                    question: 'What is Skulbot?',
                    answer: 'Skulbot is an AI-powered platform designed to help students and teachers streamline learning and teaching with automation and smart tools.',
                },
                {
                    open: false,
                    question: 'How does Skulbot help teachers?',
                    answer: 'Skulbot generates lesson plans, quizzes, and summaries, saving teachers hours of preparation time while improving student engagement.',
                },
                {
                    open: false,
                    question: 'Can students use Skulbot for exam preparation?',
                    answer: 'Yes! Skulbot provides AI-generated quizzes, study summaries, and personalized learning recommendations to help students prepare effectively.',
                },
                {
                    open: false,
                    question: 'Is Skulbot free to use?',
                    answer: 'Skulbot offers a free plan with essential features, and premium plans with advanced AI tools for enhanced learning and teaching experiences.',
                },
                {
                    open: false,
                    question: 'Does Skulbot support multiple subjects?',
                    answer: 'Yes, Skulbot works across various subjects, helping students and teachers in different fields with AI-powered content generation and assistance.',
                },
            ],

            faqs2: [
                {
                    open: false,
                    question: 'How do I sign up for Skulbot?',
                    answer: 'Simply visit our website, click on "Sign Up," and follow the easy steps to create your account.',
                },
                {
                    open: false,
                    question: 'Can I use Skulbot on my phone?',
                    answer: 'Yes! Skulbot is accessible on both desktop and mobile devices, making it easy to study and teach on the go.',
                },
                {
                    open: false,
                    question: 'Does Skulbot work with Google Classroom?',
                    answer: 'Yes, Skulbot integrates with Google Classroom and other learning management systems to enhance your teaching experience.',
                },
                {
                    open: false,
                    question: 'Is my data safe with Skulbot?',
                    answer: 'Absolutely! Skulbot follows strict security protocols to keep your data safe and private.',
                },
                {
                    open: false,
                    question: 'How do I contact support?',
                    answer: 'You can reach our support team through the "Help" section on our website or email us for assistance.',
                },
            ],
             
        }
    },
 
}

</script>