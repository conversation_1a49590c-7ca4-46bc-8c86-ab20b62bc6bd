<?php

namespace App\Http\Controllers;

use App\Models\Chat;
use Inertia\Inertia;
use Illuminate\Http\Request;

class ConversationsController extends Controller
{
    public function conversations() {
        
        $conversations = Chat::where('chatbot_id', auth()->user()->currently_selected_chatbot)->get(); 

        return Inertia::render('Conversations/Conversations', [ 
            'chatbots' => $this->chatbots(),
            'currently_selected_chatbot' => $this->selectedBot(),
            'conversations' => $conversations,
        ]);
    }
} 
