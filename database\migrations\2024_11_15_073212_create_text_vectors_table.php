<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('text_vectors', function (Blueprint $table) {
            $table->id();
            $table->json('vector');
            $table->unsignedBigInteger('user_id');
            $table->unsignedBigInteger('chatbot_id');
            $table->timestamps();

            $table->foreign('chatbot_id')->references('id')->on('chatbots')->onDelete('cascade');

        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('text_vectors');
    }
};
