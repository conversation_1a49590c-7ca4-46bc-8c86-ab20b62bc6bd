<template>
    <AppLayout title="Try My AI">
        <div class="w-full lg:h-full flex flex-col lg:flex-row items-center lg:overflow-hidden p-3 lg:p-6"> 

            <div class="w-full lg:w-1/2 lg:h-full lg:overflow-y-auto lg:overflow-x-none">
                
                <div class="w-full rounded-l-sm rounded lg:rounded-l-xl rounded-r-sm  lg:rounded-r-0 shadow-sm border-r lg:border-r-0 border-l border-y border-gray-300 p-3 lg:p-5 ">
                    <h1 class="text-lg lg:text-xl font-extrabold">Widget Settings</h1>

                    <div class="w-full flex items-center justify-between bg-gray-100 rounded-md p-1 lg:p-1.5 mt-4">
                        <div class="w-1/3">
                            <button :class="{'bg-white' : selected_settings_tab == 1 }" @click="selected_settings_tab = 1" class="w-full h-8 flex items-center justify-center space-x-2 rounded-md hover:bg-primaryColor/10">
                                <icon name="settings" class="w-3 text-gray-400"></icon>
                                <span class="text-xs md:text-sm text-gray-500">General</span>   
                            </button>
                        </div>

                        <div class="w-1/3">
                            <button :class="{'bg-white' : selected_settings_tab == 2 }" @click="selected_settings_tab = 2" class="w-full h-8 flex items-center justify-center  space-x-2 rounded-md hover:bg-primaryColor/10">
                                <icon name="plus" class="w-3 text-gray-400"></icon>
                                <span class="text-xs md:text-sm text-gray-500">Additional</span>                               
                            </button>
                        </div>

                        <div class="w-1/3">
                            <button :class="{'bg-white' : selected_settings_tab == 3 }" @click="selected_settings_tab = 3" class="w-full h-8 flex items-center justify-center  space-x-2 rounded-md hover:bg-primaryColor/10">
                                <icon name="contact_form" class="w-3 text-gray-400"></icon>
                                <span class="text-xs md:text-sm text-gray-500">Contact Form</span>   
                            </button>
                        </div>
                    </div>

                    <div v-if="selected_settings_tab == 1" class="w-full mt-10 mb-16">
                        <div class="w-full flex flex-col items-start">
                            <span class="text-sm text-gray-600">Chatbot Title</span>

                            <div class="w-full border border-gray-300 rounded-md mt-3 py-1 px-3 flex items-center space-x-3">
                                <icon name="bot" class="w-4 text-gray-500"></icon>
                                <input v-model="form.chatbot_title" type="text" class="border-none text-sm text-gray-700 focus:ring-0 w-[90%] h-8">
                            </div>
                        </div>

                        <div class="w-full flex flex-col items-start mt-5">
                            <span class="text-sm text-gray-600">Chatbot Name</span>

                            <div class="w-full border border-gray-300 rounded-md mt-2 py-1 px-3 flex items-center space-x-3">
                                <icon name="bot" class="w-4 text-gray-500"></icon>
                                <input v-model="form.bot_name" type="text" class="border-none text-sm text-gray-700 focus:ring-0 w-[90%] h-8">
                            </div>
                        </div>


                        <div class="w-full flex flex-col items-start mt-5">
                            <span class="text-sm text-gray-600">Welcome Message</span>

                            <div class="w-full mt-2">
                                <div class="flex items-center space-x-2">
                                    <button 
                                    v-for="(message , index) in welcome_messages" :key="index" 
                                    @click="form.welcome_message = message"
                                    :class="{'bg-primaryColor/20' : currentlySelectedWelcomeMessage.id == message.id}" class="w-20 border border-gray-300 px-4 py-1 text-sm text-gray-400 hover:bg-primaryColor/20 rounded-md">{{ message.type }}</button>
 
                                </div>

                                <textarea name="" id="" v-model="form.welcome_message.message"class="w-full mt-2 border border-gray-300 rounded-md" rows="2"></textarea>
                            </div>
                        </div>

                        <div class="mt-5 flex flex-col">
                            <span class="text-sm font-semibold">Primary Bot Color</span>

                            <Vue3ColorPicker style="z-index:100;" class="mt-5 shadow-sm" v-model="form.chatbot_colour" mode="solid" :showColorList="false"  :showEyeDrop="true" type="HEX"/>
                        </div>
                    </div>

                    <div v-else-if="selected_settings_tab == 2" class="w-full mt-10  mb-16">
                        <div class="w-full flex flex-col items-start">
                            <span class="text-sm text-gray-600">Profile Avatar</span>

                            <div class="mt-2">
                                <FileUpload class="mt-2" name="" :fileLimit="1" :auto="true" @select="onSelectProfileAvatar" :multiple="false"   accept="image/*" :maxFileSize="1000000">
                                    <template #empty>
                                        <div v-if="currently_selected_chatbot.chatbot_avatar != null || new_chatbot_avatar == true" class="w-14 h-14 rounded-xl mb-2"> 
                                            <img  class="w-full h-full" :src="'/storage/'+ currently_selected_chatbot.chatbot_avatar"/>
                                        </div>

                                        <span>Drag and drop file to here to upload.</span>
                                    </template>
                                </FileUpload> 
                            </div>

                        </div>

                        <div class="w-full flex flex-col items-start mt-6">
                            <span class="text-sm text-gray-600">Widget Button Icon</span>

                            <div class="mt-2">
                                <FileUpload class="mt-2" name="" :fileLimit="1" :auto="true" @select="onSelectWidgetButtonIcon" :multiple="false"   accept="image/*" :maxFileSize="1000000">
                                    <template #empty>
                                        <div v-if="currently_selected_chatbot.widget_button_icon != null || new_chatbot_widget == true" class="w-14 h-14 rounded-xl mb-2">
                                            <img class="w-full h-full" :src="'/storage/'+ currently_selected_chatbot.widget_button_icon"/>
                                        </div>

                                        <span>Drag and drop file to here to upload.</span>
                                    </template>
                                </FileUpload> 
                            </div>
                        </div>

                        <div class="w-full flex flex-col items-start mt-6">
                            <span class="text-sm text-gray-600">Widget Button Position</span>

                            <div class="w-[85%] flex items-center space-x-5 mt-2">
                                <div class="w-1/2 flex flex-col items-center">
                                    <button @click="form.widget_button_position = 'bottom-left'" :class="{'bg-gray-100 border-2 border-primaryColor' : form.widget_button_position == 'bottom-left'}" class="w-full rounded-xl border border-gray-300 pt-8 px-4 pb-2">
                                        <div class="w-full flex justify-start">
                                            <div class="w-8 h-16">
                                                <div class="w-8 h-14 rounded-lg bg-primaryColor"></div>
                                                
                                                <div class="w-full flex justify-start">
                                                    <div class="w-2 h-2 rounded-full flex-shrink-0 bg-primaryColor"></div>
                                                </div>
                                            </div>
                                        </div>
                                    </button>
                                    <span class="text-sm text-gray-400">Left</span>
                                </div>

                                <div class="w-1/2 flex flex-col items-center">
                                    <button @click="form.widget_button_position = 'bottom-right'" :class="{'bg-gray-100 border-2 border-primaryColor' : form.widget_button_position == 'bottom-right'}" class="w-full rounded-xl border border-gray-300 pt-8 px-4 pb-2">
                                        <div class="w-full flex justify-end">
                                            <div class="w-8 h-16">
                                                <div class="w-8 h-14 rounded-lg bg-primaryColor"></div>
                                                
                                                <div class="w-full flex justify-end">
                                                    <div class="w-2 h-2 rounded-full flex-shrink-0 bg-primaryColor"></div>
                                                </div>
                                            </div>
                                        </div>
                                    </button>
                                    <span class="text-sm text-gray-400">Right</span>
                                </div>
                                
                            </div>
                        </div>
 
                    </div>

                    <div  v-else-if="selected_settings_tab == 3" class="w-full mt-10  mb-16">
                        <div class="w-full mt-5 flex items-center justify-between">
                            <div class="w-[80%] flex flex-col items-start">
                                <h1 class="text sm lg:text-md font-semibold">Show Contact Form</h1>

                                <p class="text-sm text-gray-500 mt-1">The contact form will show in pop-up
                                </p>
                            </div>

                            <div class=""> 
                                <ToggleSwitch v-model="form.show_contact_form" />
                            </div>
                        </div>

                        <div v-if="form.show_contact_form" class="mt-6 w-full border border-gray-300 rounded-md p-5">
                            <h1 class="text-md lg:text-lg font-semibold">Configure Contact Form</h1>

                            <div class="w-full flex flex-col items-start mt-6">
                                <span class="text-sm text-gray-600">Number of responses before asking</span>

                                <Select v-model="form.number_of_times_before_asking" :options="number_of_times_before_asking" :checkmark="true"  :autoOptionFocus="true" optionLabel="number"  :placeholder="String(form.number_of_times_before_asking)"  class="w-full mt-5" />
                            </div>

                            <div class="w-full flex flex-col items-start mt-6">
                                <span class="text-sm text-gray-600">Number of times to ask</span>
 
                                <Select v-model="form.number_of_times_to_ask" :options="number_of_times_to_ask" :checkmark="true"  :autoOptionFocus="true" optionLabel="number" :placeholder="String(form.number_of_times_to_ask)" class="w-full mt-5" />
                            </div>

                            <div class="w-full flex flex-col items-start mt-6">
                                <span class="text-sm text-gray-600">Contact form title</span>

                                <textarea v-model="form.contact_form_title"class="w-full border border-gray-300 rounded-md mt-4 text-sm md:text-md" rows="3"></textarea>
                            </div>
                        </div> 
                    </div>
                </div>
            </div>

            <div class="w-full lg:w-1/2 mb-20 lg:mb-0 lg:h-full bg-gray-100 p-5 lg:overflow-y-auto">
                <div class="w-full">
                        <h1 class="text-md font-bold">Preview</h1>
                    </div> 

                    <div class="w-full flex justify-center mt-10">
                        <div class="w-[290px] bg-white h-[430px] rounded-2xl">
 
                            <div :style="{ backgroundColor: currentChatbotColour }" class="w-full flex justify-between items-center rounded-t-3xl h-[15%] px-5">
                                <h1 class="text-md font-extrabold text-white">{{ currentChatbotTitle }}</h1>

                                <div class="rounded-full border border-white px-2 py-1 flex items-center space-x-2 text-white text-xs">
                                    <icon name="profile" class="text-white h-4"></icon>
                                    profile
                                </div>
                            </div>

                            <div class="w-full h-[85%] rounded-b-3xl  flex flex-col justify-between">

                                <div class="w-full h-[82%] px-4 py-6">
                                    <div class="w-full flex items-end space-x-3">
                                        <div :style="{ backgroundColor: currentChatbotColour }"  class="flex-shrink-0 rounded-full w-8 h-8 p-1.5 flex justify-center flex-col items-center">
                                            <img src="/img/bot.png"  alt="">
                                        </div>

                                        <div class="bg-gray-200 rounded-3xl p-4">
                                            <p class="text-xs text-gray-800">{{ form.welcome_message.message }} </p>
                                        </div>
                                    </div>

                                    <div class="w-full flex justify-end my-3">
                                        <div :style="{ backgroundColor: currentChatbotColour }"  class=" rounded-3xl p-3">
                                            <p class="text-xs text-white">What's your name ? </p>
                                        </div>
                                    </div>

                                    <div class="w-full flex items-end space-x-3">
                                        <div :style="{ backgroundColor: currentChatbotColour }"  class="flex-shrink-0 rounded-full w-8 h-8 p-1.5 flex justify-center flex-col items-center">
                                            <img src="/img/bot.png"  alt="">
                                        </div>

                                        <div class="bg-gray-200 rounded-3xl p-4">
                                            <p class="text-xs text-gray-800">Hi there! My name is steven. How can i help you today? </p>
                                        </div>
                                    </div>
                                </div>

                                <div class="w-full h-[18%] px-3 py-2 border-t border-gray-200 flex items-center">
                                    <div :style="{ borderColor : currentChatbotColour }"  class="w-[85%] h-full rounded-lg border-2  p-2 flex items-center ">
                                        <input type="text" class="w-[90%] border-none focus:ring-0 text-xs text-gray-700" placeholder="Type a message...">
                                        <button 
                                        :style="{ backgroundColor: isHovered ? currentChatbotColour : '', color: isHovered ? 'white' : currentChatbotColour }"
                                        class="w-[12%] h-6 rounded-full hover:bg-primaryColor flex flex-col items-center justify-center group"
                                        @mouseenter="isHovered = true"
                                        @mouseleave="isHovered = false"
                                        >
                                            <icon :style="{ color: isHovered ? 'white' : currentChatbotColour }"   name="send" class="w-4 group-hover:text-white"></icon>
                                        </button>
                                    </div>

                                    <button class="w-[15%] flex justify-center">
                                        <icon :style="{ color: currentChatbotColour }" name="refresh" class="w-4 h-4 "></icon>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

            </div>
        </div>

    </AppLayout>

    <div>
        <div style="z-index:200;" class="w-[80%] fixed bottom-6 left-10 lg:left-0 flex justify-center">
            <div class="z-100 w-[350px] h-12 bg-white border border-primaryColor rounded-lg flex flex-col justify-center  px-4 py-2">
                <div class="w-full flex items-center justify-between">
                    <h1 :class="{'text-red-500' : changeCount > 0}" class="text-gray-400">Unsaved Changes </h1>

                    <button @click="changeAppearance" class="border-2 border-primaryColor py-1 px-5 text-center text-sm text-primaryColor rounded-md font-semibold hover:opacity-85 focus:opacity-85 relative">
                        Save
                        <div v-if="changeCount > 0" class="-right-2 -top-1 absolute">
                            <span class="relative flex h-3 w-3">
                                <span class="animate-ping absolute inline-flex h-full w-full rounded-full bg-red-400 opacity-75"></span>
                                <span class="relative inline-flex rounded-full h-3 w-3 bg-red-500"></span>
                            </span>
                        </div>
                    </button> 
                </div>
            </div>
        </div>
    </div>

</template>

<script> 
import AppLayout from '@/Layouts/AppLayout.vue'; 
import Icon from '@/Components/Global/Icon.vue'    
import {  Link } from '@inertiajs/vue3'; 
import {Vue3ColorPicker} from '@cyhnkckali/vue3-color-picker';
import '@cyhnkckali/vue3-color-picker/dist/style.css'
import FileUpload from 'primevue/fileupload';
import PrimaryButton from '@/Components/PrimaryButton.vue';
import ToggleSwitch from 'primevue/toggleswitch';
import Select from 'primevue/select';

export default {
    components :{ 
        AppLayout,
        Icon,
        Link, 
        Vue3ColorPicker,
        FileUpload,
        PrimaryButton,
        ToggleSwitch,
        Select
    },

    props : {
        currently_selected_chatbot : Object
    },

    data() {
        return {  
            embed_type : 1 ,
            form : {
                chatbot_colour : this.currently_selected_chatbot.chatbot_colour,
                chatbot_title : this.currently_selected_chatbot.chatbot_title,
                welcome_message : {
                    id : 1,
                    type : 'Default', 
                    message :'Welcome 👋! How can we help you today?' ,
                },
                bot_name : this.currently_selected_chatbot.bot_name ,
                profile_avatar : this.currently_selected_chatbot.chatbot_avatar,
                widget_button_icon : this.currently_selected_chatbot.widget_button_icon,
                widget_button_position : this.currently_selected_chatbot.widget_button_position,
                show_contact_form : this.currently_selected_chatbot.show_contact_form == 1 ? true : false,
                number_of_times_before_asking : this.currently_selected_chatbot.number_of_times_before_asking,
                number_of_times_to_ask : this.currently_selected_chatbot.number_of_times_to_ask,
                contact_form_title : this.currently_selected_chatbot.contact_form_title,

            },
            
            changeCount: 0,

            number_of_times_before_asking : [
                {
                    id : 1, 
                    number : 1, 
                },
                {
                    id : 2, 
                    number : 2, 
                },
                {
                    id : 3, 
                    number : 3, 
                },
                {
                    id : 4, 
                    number : 4, 
                },
            ],

            number_of_times_to_ask : [
                {
                    id : 1, 
                    number : 1, 
                },
                {
                    id : 2, 
                    number : 2, 
                }, 
            ],

            selected_settings_tab : 1,

            auth_user : this.$page.props.auth.user ,
            isHovered: false,
            welcome_message : 1,

            welcome_messages : [
                {
                    id : 1,
                    type : 'Default', 
                    message :'Welcome 👋! How can we help you today?' ,
                },
                {
                    id : 2,
                    type : 'Warm', 
                    message :'Greetings! Im here to assist you. What can I do for you today?' ,
                },
                {
                    id : 3,
                    type : 'Default', 
                    message :'What brings you here today? Feel free to ask anything.' ,
                },

            ],

            new_chatbot_avatar : false,
            new_chatbot_widget : false,

        }
    },

    methods: {  
            onSelectProfileAvatar(event) {
                this.form.profile_avatar = event.files[0];
                this.new_chatbot_avatar = true;
            },

            onSelectWidgetButtonIcon(event) {
                this.form.widget_button_icon = event.files[0];
                this.new_chatbot_widget = true;
            },

            changeAppearance(){
                this.$inertia.post(route('save_appearance_settings'), this.form , { 
                preserveState: false, 
                
            });
            }

        },

    computed : {
        
        currentChatbotColour(){

            var colour = this.form.chatbot_colour;

            return colour; 
        },

        currentChatbotTitle(){
            return this.form.chatbot_title;
        },

        currentlySelectedWelcomeMessage(){
            return this.form.welcome_message;
        },

        currentContactFormTitle(){
            return this.form.contact_form_title;
        }

    },

    mounted() { 
        this.welcome_messages.forEach(message => {
            if (message.message == this.currently_selected_chatbot.welcome_message) {
                this.form.welcome_message = message; 
            }
        }); 

        this.changeCount = -1;

    },

    watch: {
            form: {
            handler() {
                this.changeCount++;
            },
            deep: true, 
            },
        },
        }
</script>

<style>
    .p-fileupload-choose-button{
        background-color: #4682B4;
        height: 35px;
    }

    .p-fileupload-cancel-button{
        visibility:hidden;
    }
    
    .p-fileupload-upload-button {
        visibility:hidden;

    }
</style>