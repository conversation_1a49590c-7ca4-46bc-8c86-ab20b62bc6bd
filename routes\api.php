<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\ChatController;
use App\Http\Controllers\ContactsController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

Route::middleware('auth:sanctum')->get('/user', function (Request $request) {
    return $request->user();
});

// routes/api.php

Route::get('/chatbot/{chatbot_id}', [ChatController::class, 'getSettings']);

Route::get('/chatbot-iframe/{chatbot_id}', [ChatController::class, 'chatbotIframe']);


Route::post('/store-contact/{id}', [ContactsController::class, 'store_contact']);      

Route::post('/start-bot-chat', [ChatController::class, 'start_contact']);

Route::post('/send-message-in-chat/{chat_id}', [ChatController::class, 'send_message_in_chat']);

Route::post('/delete-chat/{chat_id}', [ChatController::class, 'delete_chat']);  




