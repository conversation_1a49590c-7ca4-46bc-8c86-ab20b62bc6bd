<script setup>
import { ref } from 'vue';
import { Head, Link, useForm } from '@inertiajs/vue3'; 
import Checkbox from '@/Components/Checkbox.vue';
import InputError from '@/Components/InputError.vue';
import InputLabel from '@/Components/InputLabel.vue';
import PrimaryButton from '@/Components/PrimaryButton.vue';
import TextInput from '@/Components/TextInput.vue';
import Icon from '@/Components/Global/Icon.vue';
import Carousel from 'primevue/carousel';

defineProps({
    canResetPassword: Boolean,
    status: String,
});

const form = useForm({
    email: '',
    password: '',
    remember: false,
});

const submit = () => {
    form.transform(data => ({
        ...data,
        remember: form.remember ? 'on' : '',
    })).post(route('login'), {
        onFinish: () => form.reset('password'),
    });
};

const screenshots = [
    { img: '/img/auth_shot3.png' },
    { img: '/img/auth_shot.png' },
    { img: '/img/auth_shot2.png' },
];

// Password visibility toggle
const showPassword = ref(false);
</script>

<template>
    <Head title="Log in" />

    <header class="w-full py-3 md:py-4 flex justify-between bg-white px-3 lg:px-8">
        <a href="/" class="flex items-center space-x-12">
            <img src="/img/logos/logo.webp" class="h-10 md:h-12" alt=""> 
        </a>
 
        <Link href="/register" class="bg-gradient-to-r from-primaryColor to-secondaryColor text-white text-sm tracking-wide font-workSans flex items-center py-2 px-8 rounded-full">    
            Create account
            <Icon name="chevron_right" class="w-4 ml-4 text-white"></Icon>
        </Link> 
    </header>

    <div class="min-h-screen w-full flex flex-col lg:flex-row space-y-6 lg:space-y-0 items-center bg-gray-50">
        <div class="w-full lg:w-1/2 h-full px-6 lg:px-0 py-6 flex flex-col items-center"> 
            <div class="w-full lg:w-[70%] bg-white rounded-xl shadow-md flex flex-col items-center py-10">
                <h1 class="text-lg md:text-xl font-bold text-gray-600">Sign in</h1> 

                <div v-if="status" class="mt-10 mb-4 font-medium text-sm text-green-600">
                    {{ status }}
                </div>

                <form @submit.prevent="submit" class="w-full p-6 md:p-10">
                    <div>
                        <InputLabel for="email" value="Email" class="text-gray-200" />
                        <TextInput
                            id="email"
                            v-model="form.email"
                            type="email"
                            class="mt-1 block w-full rounded-xl border border-gray-200"
                            required
                            autofocus
                            autocomplete="username"
                        />
                        <InputError class="mt-2" :message="form.errors.email" />
                    </div>

                    <div class="mt-4 relative">
                        <InputLabel for="password" value="Password" class="text-gray-200" />
                        <div class="relative w-full">
                            <TextInput
                                id="password"
                                v-model="form.password"
                                :type="showPassword ? 'text' : 'password'"
                                class="mt-1 block w-full rounded-xl border border-gray-200 pr-10"
                                required
                                autocomplete="current-password"
                            />
                            <button 
                                type="button" 
                                @click="showPassword = !showPassword" 
                                class="absolute right-3 top-1/2 transform -translate-y-1/2 flex items-center text-gray-500"
                            >
                                <Icon :name="showPassword ? 'eye_off' : 'eye'" class="w-5 h-5" />
                            </button>
                        </div>
                        <InputError class="mt-2" :message="form.errors.password" />
                    </div>

                    <div class="flex items-center justify-between mt-8">
                        <div class="block">
                            <label class="flex items-center">
                                <Checkbox v-model:checked="form.remember" name="remember" />
                                <span class="ms-2 text-sm text-gray-600">Remember me</span>
                            </label>
                        </div>

                        <Link v-if="canResetPassword" :href="route('password.request')" class="text-sm text-gray-600 hover:text-gray-900">
                            Forgot your password?
                        </Link>
                    </div>

                    <PrimaryButton class="bg-gradient-to-r from-primaryColor to-secondaryColor flex items-center mt-6 w-full rounded-xl justify-center" :class="{ 'opacity-25': form.processing }" :disabled="form.processing">
                        Sign in
                        <Icon name="chevron_right" class="w-3 ml-2"></Icon>
                    </PrimaryButton>

                    <div class="w-full mt-5 flex justify-center">
                        <div class="text-sm text-gray-400 flex flex-col md:flex-row items-center">
                            Don't have an account yet? 
                            <Link href="/register" class="text-blue-400 ml-2 hover:underline">Get started</Link>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <div class="w-full lg:w-1/2 lg:h-full p-6">
            <div class="w-full h-full rounded-xl py-12 lg:py-16 px-2 lg:px-10 bg-gradient-to-r from-primaryColor to-secondaryColor flex flex-col items-center">
                <div class="w-full lg:px-16 flex flex-col items-center">
                    <h1 class="text-xl md:text-2xl font-bold tracking-wide text-white text-center">Welcome back!</h1>
                    <h1 class="text-xl md:text-2xl font-bold tracking-wide text-white text-center">
                        Please sign in to your account to continue where you left off.
                    </h1>
                </div>

                <div class="w-full mt-10 flex flex-col items-center justify-center">
                    <div class="card">
                        <Carousel :value="screenshots" :numVisible="1" circular :autoplayInterval="3000">
                            <template #item="slotProps">
                                <div class="w-full flex justify-center">  
                                    <img :src="slotProps.data.img" class="h-[200px] object-center rounded" />   
                                </div>
                            </template>
                        </Carousel>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template> 


<style>
    .p-carousel-prev-button{
        visibility: hidden;
    } 
    .p-carousel-next-button{
        visibility: hidden;
    }
</style>
