<script setup>
import { ref } from 'vue';
import { Head, Link, useForm } from '@inertiajs/vue3'; 
import Checkbox from '@/Components/Checkbox.vue';
import InputError from '@/Components/InputError.vue';
import InputLabel from '@/Components/InputLabel.vue';
import PrimaryButton from '@/Components/PrimaryButton.vue';
import TextInput from '@/Components/TextInput.vue';
import Icon from '@/Components/Global/Icon.vue';
import Carousel from 'primevue/carousel';


const form = useForm({
    name: '',
    surname: '',
    email: '',
    password: '',
    password_confirmation: '',
    terms: false,
});

const screenshots = [
    {
        img : '/img/auth_shot3.png'
    },
    {
        img : '/img/auth_shot.png'
    },
    {
        img : '/img/auth_shot2.png'
    },
];

const submit = () => {
    form.post(route('register'), {
        onFinish: () => form.reset('password', 'password_confirmation'),
    });
};

// Password visibility toggle
const showPassword = ref(false);

</script>

<template>
    <Head title="Register" />

    <header class="w-full py-3 md:py-4 flex justify-between bg-white px-3 lg:px-8">
        <a href="/" class="flex items-center space-x-12">
            <img src="/img/logos/logo.webp" class="h-10 md:h-12" alt=""> 
        </a>

        <div class="flex items-center space-x-5"> 

            <Link href="/login" class="bg-gradient-to-r from-primaryColor to-secondaryColor text-white text-sm tracking-wide font-workSans flex items-center  py-2 px-8 rounded-full">    
                Log in
                <icon name="chevron_right" class="w-4 ml-4 text-white"></icon>
            </Link>
        </div>
    </header>

    <div class="min-h-screen w-full flex flex-col lg:flex-row space-y-6 lg:space-y-0  items-center bg-gray-50">
        <div class="w-full lg:w-1/2 h-full px-6 lg:px-0 py-16 flex flex-col items-center"> 

            <div class="w-full lg:w-[70%] bg-white rounded-xl shadow-md mt-8 flex flex-col items-center py-10">
                <h1 class="text-xl font-bold text-gray-600">Sign up</h1> 

                <form @submit.prevent="submit" class="w-full p-4 md:p-10">
                    <div class="w-full flex flex-col lg:flex-row space-y-5 lg:space-y-0 items-center lg:space-x-4">
                        <div class="w-full lg:w-1/2">
                            <InputLabel for="name" value="Name" />
                            <TextInput
                                id="name"
                                v-model="form.name"
                                type="text"
                                class="mt-1 block w-full rounded-xl border border-gray-200"
                                required
                                autofocus
                                autocomplete="name"
                            />
                            <InputError class="mt-2" :message="form.errors.name" />
                        </div>

                        <div class="w-full lg:w-1/2">
                            <InputLabel for="surname" value="Surname" />
                            <TextInput
                                id="surname"
                                v-model="form.surname"
                                type="text"
                                class="mt-1 block w-full rounded-xl border border-gray-200"
                                required
                                autofocus
                                autocomplete="surname"
                            />
                            <InputError class="mt-2" :message="form.errors.surname" />
                        </div>
                        
                    </div>

                    <div class="mt-4">
                        <InputLabel for="email" value="Email" />
                        <TextInput
                            id="email"
                            v-model="form.email"
                            type="email"
                            class="mt-1 block w-full rounded-xl border border-gray-200"
                            required
                            autocomplete="username"
                        />
                        <InputError class="mt-2" :message="form.errors.email" />
                    </div>


                    <div class="mt-4">
                        <InputLabel for="password" value="Password" />

                        <div class="relative w-full">
                            <TextInput
                            id="password"
                            v-model="form.password"
                            :type="showPassword ? 'text' : 'password'"
                            class="mt-1 block w-full rounded-xl border border-gray-200"
                            required
                            autocomplete="new-password"
                        />
                            <button 
                                type="button" 
                                @click="showPassword = !showPassword" 
                                class="absolute right-3 top-1/2 transform -translate-y-1/2 flex items-center text-gray-500"
                            >
                                <Icon :name="showPassword ? 'eye_off' : 'eye'" class="w-5 h-5" />
                            </button>
                        </div> 
                        
                        <InputError class="mt-2" :message="form.errors.password" />
                    </div>

                    <div class="mt-4">
                        <InputLabel for="password_confirmation" value="Confirm Password" />

                        <div class="relative w-full">
                            <TextInput
                            id="password_confirmation"
                            v-model="form.password_confirmation"
                            :type="showPassword ? 'text' : 'password'"
                            class="mt-1 block w-full rounded-xl border border-gray-200"
                            required
                            autocomplete="new-password"
                        />
                            <button 
                                type="button" 
                                @click="showPassword = !showPassword" 
                                class="absolute right-3 top-1/2 transform -translate-y-1/2 flex items-center text-gray-500"
                            >
                                <Icon :name="showPassword ? 'eye_off' : 'eye'" class="w-5 h-5" />
                            </button>
                        </div> 
 
                        <InputError class="mt-2" :message="form.errors.password_confirmation" />
                    </div>

                    <div v-if="$page.props.jetstream.hasTermsAndPrivacyPolicyFeature" class="mt-4">
                        <InputLabel for="terms">
                            <div class="flex items-center">
                                <Checkbox id="terms" v-model:checked="form.terms" name="terms" required />

                                <div class="ms-2">
                                    I agree to the <a target="_blank" :href="route('terms.show')" class="text-sm text-gray-600 hover:text-gray-900 rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">Terms of Service</a> and <a target="_blank" :href="route('policy.show')" class=" text-sm text-gray-600 hover:text-gray-900 rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">Privacy Policy</a>
                                </div>
                            </div>
                            <InputError class="mt-2" :message="form.errors.terms" />
                        </InputLabel>
                    </div>

                    <div class="flex items-center justify-between mt-5">
                        <PrimaryButton class="bg-gradient-to-r from-primaryColor to-secondaryColor flex items-center mt-6 w-full rounded-xl justify-center" :class="{ 'opacity-25': form.processing }" :disabled="form.processing"> 
                            Register
                            <icon name="chevron_right" class="w-4 ml-2"></icon>
                        </PrimaryButton>
 
                    </div>

                    <div class="w-full mt-5 flex justify-center">
                        <div class="text-sm text-gray-400 flex items-center">Already have an account?<Link href="login" class="text-blue-400 ml-2 hover:underline">Log in</Link></div>
                    </div>
                </form>
            </div>
        </div>

        <div class="w-full lg:w-1/2 lg:h-full p-6">
            <div class="w-full h-full rounded-xl py-12 lg:py-16 px-2 lg:px-10 bg-gradient-to-r from-primaryColor to-secondaryColor flex flex-col items-center">
                <div class="w-full lg:px-16 flex flex-col items-center">
                    <h1 class="text-xl md:text-2xl font-bold tracking-wide text-white text-center">Join us today!</h1>
                    <h1 class="text-xl md:text-2xl font-bold tracking-wide text-white text-center">
                        Sign up now and unlock amazing features.
                    </h1>
                </div>

                <div class="w-full mt-10 flex flex-col items-center justify-center">
                    <div class="card">
                        <Carousel :value="screenshots" :numVisible="1" circular :autoplayInterval="3000">
                            <template #item="slotProps">
                                <div class="w-full flex justify-center">  
                                    <img :src="slotProps.data.img" class="h-[200px] object-center rounded" />   
                                </div>
                            </template>
                        </Carousel>
                    </div>
                </div>
            </div>
        </div>

 
    </div>
 
</template>

<style>
    .p-carousel-prev-button{
        visibility: hidden;
    } 
    .p-carousel-next-button{
        visibility: hidden;
    }
</style>