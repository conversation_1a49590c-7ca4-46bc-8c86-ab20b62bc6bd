<?php

namespace App\Http\Controllers;

use App\Models\Chat;
use Inertia\Inertia;
use App\Models\AiFaq;
use App\Models\Chatbot;
use Illuminate\Support\Str;
use Illuminate\Http\Request;
use App\Services\VectorService;
use OpenAI\Laravel\Facades\OpenAI;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\View;

class ChatController extends Controller
{
    private $apiKey;
    private $baseUrl;

    public function __construct()
    {
        $this->apiKey = env('PINECONE_API_KEY');
        $this->baseUrl = 'https://api.pinecone.io/assistant';
    }

    public function send_message_in_chat(Request $request, $chat_id)
    {
        $input = $request->message;

        // Fetch chat and chatbot details
        $defaultchat = Chat::where('id', $chat_id)->first();

        $selected_chatbot = Chatbot::where('id', $defaultchat->chatbot_id)->first();

        // Retrieve and decode existing chat context
        $messages_no_ai = json_decode($defaultchat->context, true) ?? [];

        $messages_no_ai[] = ['role' => 'user', 'content' => $input];

        $userId = auth()->user()->id; 
        $chatbot = $this->selectedBot();
        
        $chatbotId = $chatbot->id; 

        $assistant_name = 'assistant' . $userId . $chatbotId ;
 
        $url = "https://prod-1-data.ke.pinecone.io/assistant/chat/" . $assistant_name;
    
        $response = Http::withHeaders([
            'Api-Key' => $this->apiKey,
            'Content-Type' => 'application/json',
            'X-Pinecone-API-Version' => '2025-01',
        ])->post($url, [
            'messages' => [
                [
                    'role' => 'user',
                    'content' => $input,
                ]
            ],
            'json_response' => true,
            'model' => 'gpt-4o',
        ]);
    
        $res = $response->json(); 

        // dd($res);

        if ($res['message']) { 
            $message = json_decode($res['message']['content'], true);  
        }else {
            $message = dd($res);   
        }


        $messages_no_ai[] = ['role' => 'assistant', 'content' => $message]; 


        // Save updated context
        $defaultchat->forceFill([
            'context' => json_encode($messages_no_ai),
        ])->save();

        return ['response' => $message];

    }

    /**
     * Truncate messages to fit within a token limit.
     *
     * @param array $messages
     * @param int $maxTokens
     * @return array
     */
    private function truncateMessages(array $messages, int $maxTokens): array
    {
        $currentTokens = 0;
        $truncatedMessages = [];

        foreach (array_reverse($messages) as $message) {
            $messageTokens = ceil(mb_strlen($message['content']) / 4); // Approximate token count
            if ($currentTokens + $messageTokens > $maxTokens) {
                break;
            }
            $currentTokens += $messageTokens;
            array_unshift($truncatedMessages, $message); // Add back in correct order
        }

        // Always ensure at least the latest user message and system instructions are kept
        if (empty($truncatedMessages)) {
            $truncatedMessages = array_slice($messages, -2); // Keep last two messages
        }

        return $truncatedMessages;
    }

    public function delete_chat($id)
    {
        try {
            $chat = Chat::findOrFail($id);
            $chat->forceFill(['context' => json_encode([])])->save();

            return response()->json(['message' => 'Chat context cleared.']);
        } catch (\Throwable $th) {
            \Log::error('Error in delete_chat: ' . $th->getMessage());
            return response()->json(['error' => 'Failed to delete chat.'], 500);
        }
    }

    public function start_contact(Request $request) {
        $chatbot = Chatbot::findOrFail($request->chatbot_id);
    
        $chat = Chat::create([
            'chatbot_id' => $chatbot->id,
            'user_id' => $chatbot->user_id,
            'random_id' => Str::uuid(),
            'context' => $chatbot->welcome_message,
            'context_added' => false,
            'session_id' => session()->getId(),
            'ip' => $request->ip(),
            'agent' => $request->header('User-Agent'),
            'test_chat' => false,
        ]);
    
        return response()->json(['chat_id' => $chat->id, 'message' => $chatbot->welcome_message]);
    }

    public function getSettings($chatbot_id)
    {
        $chatbot = Chatbot::where('random_id', $chatbot_id)->first();

        if (!$chatbot) {
            return response()->json(['error' => 'Chatbot not found'], 404);
        }

        return response()->json($chatbot);
    }

    public function chatbotIframe($chatbot_id)
    {
        $chatbot = Chatbot::where('random_id', $chatbot_id)->first();

        if (!$chatbot) {
            return response()->json(['error' => 'Chatbot not found'], 404);
        }

        $chat_instance = Chat::where('ip', request()->ip())->where('agent', request()->header('User-Agent'))->first();

        if (!$chat_instance) {
            $chat_instance = Chat::create([
                'chatbot_id' => $chatbot->id, 
                'random_id' => $this->randomNumber(),  
                'context' => json_encode([]),  
                'session_id' => null,
                'ip' =>  request()->ip(),
                'agent' => request()->header('User-Agent'),
                'test_chat' => true, 
                'context_added' => true, 
            ]); 
        }

        $all_messages = null;

        if ($chat_instance->context != []) {
            $all_messages = json_decode($chat_instance->context); 
        }

        return Inertia::render('TryMyAi/MyAiBotIframe', [ 
            'chat' => $chat_instance, 
            'all_messages' => $all_messages, 
            'currently_selected_chatbot' => $chatbot, 
        ]); 

    }


}
