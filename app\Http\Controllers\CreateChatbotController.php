<?php

namespace App\Http\Controllers;

use DOMXPath;
use Exception;
use DOMDocument;
use GuzzleHttp\Client;
use GuzzleHttp\Promise\Utils;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;

class CreateChatbotController extends Controller
{  
    public function get_url_links(Request $request)
    {
        $url = $request->website;

        if (!filter_var($url, FILTER_VALIDATE_URL)) {
            return response()->json(['error' => 'Invalid URL provided.'], 400);
        }

        try {
            $response = Http::timeout(10)->withOptions(['allow_redirects' => true])->get($url);
        } catch (Exception $e) {
            return response()->json(['error' => 'Unable to fetch the URL. ' . $e->getMessage()], 400);
        }

        if (!$response->successful()) {
            return response()->json(['error' => 'Unable to retrieve the content from the URL.'], 400);
        }

        $htmlContent = $response->body();
        $dom = new \DOMDocument();

        libxml_use_internal_errors(true);
        $dom->loadHTML($htmlContent);
        libxml_clear_errors();

        $xpath = new \DOMXPath($dom);
        $anchorNodes = $xpath->query("//a[@href]");

        $importantKeywords = ['product', 'blog', 'article', 'support', 'learn', 'faq', 'guide', 'about', 'contact', 'pricing', 'features', 'case-study', 'resources', 'testimonials', 'portfolio', 'careers', 'team'];
        $excludeKeywords = ['login', 'mail', 'tel', 'signup', 'register', 'privacy', 'terms', 'javascript:void', 'logout', 'wp-admin', 'dashboard'];

        $rawLinks = collect();

        foreach ($anchorNodes as $link) {
            $href = $link->getAttribute('href');
            $linkText = strtolower($link->textContent);

            if (!$this->isValidLink($href, $linkText, $importantKeywords, $excludeKeywords)) {
                continue;
            }

            $href = $this->normalizeUrl($href, $url);
            $rawLinks->push($href);
        }

        $uniqueLinks = $rawLinks->unique()->take(15)->values(); // Only check top 15 links

        // Validate those links with parallel requests
        $validatedLinks = $this->validateLinksInParallel($uniqueLinks);

        return response()->json(['links' => $validatedLinks->values()]);
    }
        
    private function validateLinksInParallel($links)
{
    $client = new Client(['timeout' => 10]);
    $promises = [];

    foreach ($links as $link) {
        $promises[$link] = $client->getAsync($link);
    }

    $results = collect();
    $responses = Utils::settle($promises)->wait();

    foreach ($responses as $link => $response) {
        if ($response['state'] === 'fulfilled') {
            $body = (string) $response['value']->getBody();

            if (strlen(strip_tags($body)) > 300) { // Simple check for meaningful content
                $results->push($link);
            }
        }
    }

    return $results;
}


    
    /**
     * Check if a link is valid and matches keywords.
     */
    private function isValidLink($href, $text, $importantKeywords, $excludeKeywords)
    {
        if (empty($href) || str_starts_with($href, '#')) return false;
    
        foreach ($excludeKeywords as $keyword) {
            if (stripos($href, $keyword) !== false || stripos($text, $keyword) !== false) {
                return false;
            }
        }
    
        foreach ($importantKeywords as $keyword) {
            if (stripos($href, $keyword) !== false || stripos($text, $keyword) !== false) {
                return true;
            }
        }
    
        return false;
    }
    
    /**
     * Normalize and clean up URLs.
     */
    private function normalizeUrl($href, $baseUrl) {
        $href = strtok($href, '#'); // Remove fragments
        $href = rtrim($href, '/'); // Remove trailing slashes
    
        $parsedUrl = parse_url($href);
        if (!isset($parsedUrl['scheme'])) {
            $href = rtrim($baseUrl, '/') . '/' . ltrim($href, '/');
        }
    
        return $href;
    }
    
    /**
     * Helper function to check if a string contains any of the provided keywords.
     */
    private function containsAny($text, $keywords) {
        foreach ($keywords as $keyword) {
            if (stripos($text, $keyword) !== false) {
                return true;
            }
        }
        return false;
    }

    private function hasTextContent($url): bool
    {
        try {
            $response = Http::timeout(10)->withOptions(['allow_redirects' => true])->get($url);

            if (!$response->successful()) {
                return false;
            }

            $html = $response->body();
            $dom = new DOMDocument();
            libxml_use_internal_errors(true);
            $dom->loadHTML($html);
            libxml_clear_errors();

            $textContent = trim(strip_tags($dom->textContent));

            // Minimum text length threshold (you can tweak this)
            return strlen($textContent) > 100;
        } catch (Exception $e) {
            return false;
        }
    }

    
    
}
