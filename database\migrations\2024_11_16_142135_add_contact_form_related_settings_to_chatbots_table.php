<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('chatbots', function (Blueprint $table) {
            $table->boolean('show_contact_form')->default(true);
            $table->Integer('number_of_times_before_asking')->default(4);
            $table->Integer('number_of_times_to_ask')->default(1);
            $table->String('contact_form_title')->default('Can I please have your contact, so we can forward you more information?');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('chatbots', function (Blueprint $table) {
            $table->dropColumn('number_of_times_before_asking','number_of_times_to_ask','contact_form_title' );
        });
    }
};
