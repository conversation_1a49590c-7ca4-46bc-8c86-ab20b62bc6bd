<?php

namespace App\Http\Controllers;

use App\Models\Chat;
use Inertia\Inertia;
use App\Models\Chatbot; 
use Illuminate\Http\Request;

class DashboardController extends Controller
{
    public function dashboard() {

        $user_chats = Chat::where('user_id', auth()->user()->id)->with('contacts')->get();

        $contacts = $user_chats->pluck('contacts')->flatten()->count();
          
        $conversations = Chat::where('chatbot_id', auth()->user()->currently_selected_chatbot)->get()->count(); 

        $chats = Chat::where('chatbot_id', auth()->user()->currently_selected_chatbot)->get();

        $responses = 0;

        foreach ($chats as $chat) {
            $messages = json_decode($chat->context, true);

            if (is_array($messages)) {
                $responses += collect($messages)
                    ->where('role', 'assistant')
                    ->count();
            }
        } 
        
        return Inertia::render('Dashboard', [ 
            'chatbots' => $this->chatbots(),
            'currently_selected_chatbot' => $this->selectedBot(),
            'contacts' => $contacts,
            'conversations' => $conversations,
            'responses' => $responses,
            
        ]);
        
    }
}  
