<?php

namespace App\Http\Controllers;

use App\Models\Chat;
use Inertia\Inertia;
use App\Models\AiFaq;
use App\Models\Chatbot;
use Illuminate\Http\Request;
use App\Services\VectorService;
use OpenAI\Laravel\Facades\OpenAI;

class TryMyAiController extends Controller
{
    public function demo() { 

        $selected_chatbot = Chatbot::where('id', auth()->user()->currently_selected_chatbot)->first();

        $defaultchat = Chat::where('user_id', auth()->user()->id)->where('test_chat', true)->first(); 
        
        if (!$defaultchat) {
            
            $defaultchat = Chat::create([
                'chatbot_id' => $selected_chatbot->id,
                'user_id' => auth()->user()->id,
                'random_id' => $this->randomNumber(),  
                'context' => json_encode([]),  
                'session_id' => request()->getSession()->getId(),
                'ip' =>  request()->ip(),
                'agent' => request()->header('User-Agent'),
                'test_chat' => true, 
                'context_added' => true, 
                
            ]); 
            
            $add_faq = '';

            $frequently_asked_questions = AiFaq::where('user_id', auth()->user()->id)->get();

            if (count($frequently_asked_questions) > 0) {
                $add_faq = "Before Answering any question - AI assistant will first check if the question asked is the one below , if yes - the it give the answer provided :\n\n";
            
                foreach ($frequently_asked_questions as $faq) {
                    $add_faq .= "Question: " . $faq->question . "\nAnswer: " . $faq->answer . "\n\n";
                }
            } 

            $default_message = 
                "AI assistant is a brand new, powerful, human-like artificial intelligence.".
                $add_faq .
                "DO NOT SHARE REFERENCE URLS THAT ARE NOT INCLUDED IN THE CONTEXT BLOCK.
                AI assistant will not apologize for previous responses, but instead will indicated new information was gained.
                If user asks about or refers to the current 'workspace' AI will refer to the the content after START CONTEXT BLOCK and before END OF CONTEXT BLOCK as the CONTEXT BLOCK. 
                If AI sees a REFERENCE URL in the provided CONTEXT BLOCK, please use reference that URL in your response as a link reference right next to the relevant information in a numbered link format e.g. ([reference number](link))
                If link is a pdf and you are CERTAIN of the page number, please include the page number in the pdf href (e.g. .pdf#page=x ).
        
                If AI is asked to give quotes, please bias towards providing reference links to the original source of the quote.
                AI assistant will take into account any CONTEXT BLOCK that is provided in a conversation. It will say it does not know if the CONTEXT BLOCK is empty.
                AI assistant will not invent anything that is not drawn directly from the context.
                AI assistant will not answer questions that are not related to the context.

                ";

            $messages[] = ['role' => 'system', 'content' => $default_message];

            $defaultchat->forceFill([
                'context' => json_encode($messages),
            ])->save();
        } 
         
        $all_messages = null;

        if ($defaultchat->context != []) {
            $all_messages = json_decode($defaultchat->context); 
        }

        
        return Inertia::render('TryMyAi/TryMyAi', [ 
            'chatbots' => $this->chatbots(),
            'currently_selected_chatbot' => $this->selectedBot(),
            'all_messages' => $all_messages,
            'chat' => $defaultchat,
        ]);
        
    }
}
