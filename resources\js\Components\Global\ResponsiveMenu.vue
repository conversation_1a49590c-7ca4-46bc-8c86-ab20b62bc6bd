<template>

<button @click="setIsOpen(true)" class="p-1 hover:bg-opacity-75 rounded-full z-50">
    <icon name="responsive_menu_icon" class="text-primaryColor h-6 w-6"></icon>
</button>

<TransitionRoot appear as="template" :show="isOpen">
    <Dialog 
    
    class=" fixed inset-0 overflow-hidden z-50"
    :open="isOpen" @close="setIsOpen">

        <TransitionChild 
            enter="transition-opacity ease-in-out duration-300"
            enter-from="opacity-0"
            enter-to="opacity-100"
            leave="transition-opacity ease-in-out duration-300"
            leave-from="opacity-100"
            leave-to="opacity-0"
            as="template">
            <DialogOverlay class="absolute inset-0 bg-black bg-opacity-40 " />
        </TransitionChild>

        <TransitionChild 
            enter="transform ease-in-out transition-transform duration-300"
            enter-from="translate-x-full"
            enter-to="translate-x-0"
            leave="transform ease-in-out transition-transform duration-300"
            leave-from="translate-x-0"
            leave-to="translate-x-full"
            as="template">
            <div class="bg-white fixed inset-y-0 right-0 h-full w-full max-w-xs "> 
                <div class="flex items-center justify-between px-2 py-2 shadow">
                    
                    <img src="/img/logos/logo.webp" class="h-12 lg:h-14" alt="">
                    
                    <button @click="setIsOpen(false)" class="p-1 rounded-full focus:outline-none focus:bg-gray-100">
                        <icon name="x" class="w-6 h-6"></icon>
                    </button>
                </div>

                <div class="mt-2 overflow-y-scroll h-full pb-28">
                    <DialogDescription class="overflow-y-scroll h-full ">
                         
                        <div class="flex flex-col space-y-2 overflow-y-auto">
                            <div class="w-full mt-4 pl-3">
                                <h5 class="text-xs text-gray-400 font-semibold">Main Menu</h5>
                            </div>

                            <div class="flex flex-col">
                                <div v-for="(sidebarlink, index) in user_topsidebarlinks" :key="index">
                                    <Link :href="route(sidebarlink.link)"  :class="{'bg-primaryColor text-white' : isRoute(sidebarlink.link)}" class="w-full space-x-4 px-4 py-4 hover:bg-purple-100 focus:bg-purple-100 flex items-center rounded-l-lg">
                                        <icon :name="sidebarlink.icon" :class="{'text-white' : isRoute(sidebarlink.link)}" class="w-4  text-gray-800"></icon>
                                        <span :class="{'text-white font-bold' : isRoute(sidebarlink.link)}" class="text-md font-semibold text-gray-800">
                                            {{sidebarlink.name}}
                                        </span>
                                    </Link>
                                </div>
                            </div>

                            
                            <div class="w-full mt-4 pl-3">
                                <h5 class="text-xs text-gray-400 font-semibold">Other Links</h5>
                            </div>

                            <div class="flex flex-col">
                                <div v-for="(sidebarlink, index) in bottom_links" :key="index">
                                    <Link :href="route(sidebarlink.link)"  :class="{'bg-primaryColor text-white' : isRoute(sidebarlink.link)}" class="w-full space-x-4 px-4 py-4 hover:bg-purple-100 focus:bg-purple-100 flex items-center rounded-l-lg">
                                        <icon :name="sidebarlink.icon" :class="{'text-white' : isRoute(sidebarlink.link)}" class="w-4  text-gray-800"></icon>
                                        <span :class="{'text-white font-bold' : isRoute(sidebarlink.link)}" class="text-md font-semibold text-gray-800">
                                            {{sidebarlink.name}}
                                        </span>
                                    </Link>
                                </div>
                            </div>
                        </div> 
                    </DialogDescription>     
                </div>
 

            </div>
        </TransitionChild>

        
    </Dialog>
</TransitionRoot>

</template>

<script>
  import { ref } from "vue";
  import {
    Dialog,
    DialogOverlay,
    DialogTitle,
    DialogDescription,
    TransitionRoot,
    TransitionChild,
    Menu, MenuButton, MenuItems, MenuItem
  } from "@headlessui/vue"; 
    import Icon from '@/Components/Global/Icon.vue'  
    import { Link } from '@inertiajs/vue3';

  export default {
    components: { 
        Icon,
        Link,
        TransitionRoot,
        TransitionChild, 
        Dialog, DialogOverlay, 
        DialogTitle, 
        DialogDescription ,
        Menu, MenuButton, MenuItems, MenuItem
    },

    data() {
        return { 
                user_topsidebarlinks:[
                {
                            'name' : 'Dashboard',
                            'icon' : 'home',
                            'link' : 'dashboard', 
                        }, 

                        {
                            'name' : 'Appearance',
                            'icon' : 'appearance',
                            'link' : 'appearance', 
                        },  
                         
                        {
                            'name' : 'Try My AI',
                            'icon' : 'try_chat',
                            'link' : 'demo', 
                        }, 
                        {
                            'name' : 'Training Materials',
                            'icon' : 'training_material',
                            'link' : 'training_materials', 
                        }, 

                        {
                            'name' : 'Question & Answers',
                            'icon' : 'questions_answers',
                            'link' : 'faq_responses', 
                        },  

                ],

                bottom_links : [
                        {
                            'name' : 'Conversations',
                            'icon' : 'conversations',
                            'link' : 'conversations', 
                        }, 
                        {
                            'name' : 'Captured Contacts',
                            'icon' : 'contacts',
                            'link' : 'contacts', 
                        },       
                        {
                            'name' : 'Settings',
                            'icon' : 'settings',
                            'link' : 'profile.show', 
                        }, 
                    ],
                
        }
    },

    setup() {
      let isOpen = ref(false);

      return {
        isOpen,
        setIsOpen(value) {
          isOpen.value = value;
        }, 
      };
    },

    methods: { 
            logout() {
                this.$inertia.post(route('logout'));
            }, 
    },
  };
</script>