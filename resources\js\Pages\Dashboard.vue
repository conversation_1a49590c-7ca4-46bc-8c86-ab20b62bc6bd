<template>
    <AppLayout title="Dashboard">
        <div class="p-5 lg:p-16">
            <div class="flex items-center space-x-3">
                <div class="w-10 lg:w-12 h-10 lg:h-12 rounded-lg bg-primaryColor flex flex-col items-center justify-center text-xl font-extrabold text-white">{{ currently_selected_chatbot.chatbot_title[0] }} </div>

                <h1 class="text-md lg:text-xl font-medium text-gray-700">{{ currently_selected_chatbot.chatbot_title }}</h1>
            </div>

            <div class="mt-6 w-full border border-gray-200 rounded-lg shadow-sm">
                <div class="w-full rounded-t-lg border-b border-gray-300 flex items-center space-x-2 px-6 py-5">
                    <icon name="data" class="w-5 text-primaryColor"></icon>
                    <h2 class="text-md font-semibold text-gray-600"> Statistics</h2>
                </div>

                <div class="p-4 lg:p-6">
                    <div class="w-full flex flex-col lg:flex-row space-y-6 lg:space-y-0 lg:items-stretch space-x-0 lg:space-x-4">
                        <div class="w-full lg:w-1/3 border border-gray-200 rounded-lg p-4">
                            <div class="flex flex-col">
                                <div class="flex items-center space-x-2">
                                    <icon name="conversations" class="w-4 text-gray-400"></icon>

                                    <span class="text-md font-medium text-gray-600">Conversations</span>

                                </div>
                                <h1 class="text-3xl mt-3 font-extrabold text-gray-700">{{ $page.props.conversations }}</h1>


                                <Link :href="route('conversations')" class="w-full border border-gray-300 rounded-md text-sm text-center py-2 mt-6 hover:bg-primaryColor/10">All Conversations</Link>
                            </div>
                        </div>
                        
                        <div class="w-full lg:w-1/3 border border-gray-200 rounded-lg p-4">
                            <div class="flex flex-col">
                                <div class="flex items-center space-x-2">
                                    <icon name="responses" class="w-4 text-gray-400"></icon>

                                    <span class="text-md font-medium text-gray-600">Responses</span>
                                </div>
                                <h1 class="text-3xl mt-3 font-extrabold text-gray-700">
                                    {{ $page.props.responses }}
                                </h1>


                                <Link :href="route('demo')" class="w-full border border-gray-300 rounded-md text-sm text-center py-2 mt-6 hover:bg-primaryColor/10">Try Ai</Link>
                            </div>
                        </div>

                        <div class="w-full lg:w-1/3 border border-gray-200 rounded-lg p-4">
                            <div class="flex flex-col">
                                <div class="flex items-center space-x-2">
                                    <icon name="leads" class="w-4 text-gray-400"></icon>

                                    <span class="text-md font-medium text-gray-600">Contacts</span>

                                </div>
                                <h1 class="text-3xl mt-3 font-extrabold text-gray-700">{{ $page.props.contacts }}</h1>


                                <Link :href="route('contacts')" class="w-full border border-gray-300 rounded-md text-sm text-center py-2 mt-6 hover:bg-primaryColor/10">All Contacts</Link>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="mt-6 w-full border border-gray-200  rounded-lg shadow-sm">
                <div class="w-full rounded-t-lg border-b border-gray-300 flex items-center space-x-2 px-6 py-5">
                    <icon name="code" class="w-5 text-primaryColor"></icon>
                    <h2 class="text-md font-semibold text-gray-600">Add Widget to Website</h2>
                </div>

                <div class="w-full p-3 lg:p-6">
                    <div class="flex items-center space-x-5">
                        <h3 class="text-md text-gray-500">Embed Using</h3>

                        <div class="flex items-center bg-gray-200 rounded-md p-2">
                            <button :class="{'bg-white' : embed_type == 1}" @click="embed_type = 1" class="px-4 py-1 text-xs rounded-md">HTML</button> 
                        </div>
                    </div>

                    <div class="w-full">
                        <p v-if="embed_type == 1" class="text-xs lg:text-sm text-gray-600 mt-5"> To embed your chatbot into your website as a block chat interface , simply add the following code snipped in the head of your html document , just before the body. If you have a parent layout add it there to make the bot appear in all your templates! </p>
    
                        <div v-if="embed_type == 1" class="mt-4 lg:p-5">
                            <div class="bg-gray-100 lg:p-6 p-3">
                                <div ref="codeSnippet" class="text-xs lg:text-sm text-left">
                                    &lt;script&gt; <br>
                                        window.chatbotId = '{{ $page.props.currently_selected_chatbot.random_id }}';<br>
                                    &lt;/script&gt; <br><br>
    
                                    &lt;script src="https://app.getkimana.com/embed.js"&gt;&lt;/script&gt;
                                </div>
    
                                <div class="mt-4">
                                    <button @click="copyCode" class="border bg-white hover:border-primaryColor focus:bg-primaryColor/20 focus:font-bold border-gray-300 rounded-lg px-4 py-1 text-sm text-gray-700">
                                        <span v-if="code_copied">copied!</span>
                                        <span v-else>copy</span>
                                    </button>
                                </div>
                            </div> 
                        </div> 
                    </div>
                </div>
            </div> 
             
        </div>
    </AppLayout>
</template>

<script> 
import AppLayout from '@/Layouts/AppLayout.vue'; 
import Icon from '@/Components/Global/Icon.vue'    
import {  Link } from '@inertiajs/vue3'; 

export default {
    components :{ 
        AppLayout,
        Icon,
        Link, 
    },

    props : {
        currently_selected_chatbot : Object
    },

    data() {
        return {  
            embed_type : 1,
            code_copied : false ,
        }
    },

    methods: { 
        copyCode(){
            const codeText = this.$refs.codeSnippet.innerText;

            navigator.clipboard.writeText(codeText)
            .then(() => {
            this.code_copied = true;
            setTimeout(() => {
                this.code_copied = false;
            }, 2000);
            })
            .catch(err => {
            console.error('Failed to copy: ', err); 
            });


        }
        },
}
</script>