<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Chatbot extends Model
{
    use HasFactory;

    protected $fillable =[
        'user_id',
        'random_id',
        'chatbot_title',
        'chatbot_colour',
        'chatbot_avatar', 
        'bot_name', 
        'welcome_message', 
        'widget_button_icon', 
        'widget_button_position', 
        'number_of_times_before_asking',
        'number_of_times_to_ask',
        'contact_form_title',
        'show_contact_form',
    ];
}
