<?php

namespace App\Models;

use App\Models\ChatContact;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Chat extends Model
{
    use HasFactory;

    protected $fillable = [
        'chatbot_id',
        'user_id',
        'random_id',
        'context',
        'context_added',
        'session_id',
        'ip',
        'agent',
        'test_chat',
        'contact_sent',
    ];


    public function contacts(){
        return $this->hasMany(ChatContact::class , 'chat_id'); 
    }
}
