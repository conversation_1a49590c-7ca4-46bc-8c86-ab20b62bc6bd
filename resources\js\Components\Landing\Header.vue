<template>
     <header class="w-full py-2 lg:py-4 bg-white px-2 md:px-5 lg:px-8 sticky top-0">
        <div class="w-full flex justify-between">
            <Link href="/" class="flex items-center space-x-2 lg:space-x-4">
                <img src="/img/logos/logo_long.webp" class="h-7 md:h-10" alt="">  
            </Link>
    
            <div class="hidden lg:flex items-center space-x-10">
                <Link :href="route('about')" class="text-sm hover:text-primaryColor tracking-wide font-workSans text-gray-700">About Us</Link>
                <Link :href="route('faqs')" class="text-sm hover:text-primaryColor tracking-wide font-workSans text-gray-700">FAQs</Link>
                <Link :href="route('contact')" class="text-sm hover:text-primaryColor tracking-wide font-workSans text-gray-700">Contact Us</Link>
            </div>
    
    
            <div class="hidden lg:flex items-center space-x-5">
                <Link href="/login" class="text-md font-workSans "> Sign in</Link>
    
                <Link href="register" class="bg-gradient-to-r from-primaryColor to-secondaryColor  text-white text-md font-workSans flex items-center  py-2 px-6 rounded-full">    
                    Register 
                </Link>
            </div>

            <div class="-mr-2 flex items-center lg:hidden">
                <button @click="showingNavigationDropdown = ! showingNavigationDropdown" class="inline-flex items-center justify-center p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 focus:text-gray-500 transition">
                    <svg class="h-6 w-6" stroke="currentColor" fill="none" viewBox="0 0 24 24">
                        <path :class="{'hidden': showingNavigationDropdown, 'inline-flex': ! showingNavigationDropdown }" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
                        <path :class="{'hidden': ! showingNavigationDropdown, 'inline-flex': showingNavigationDropdown }" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div> 
        </div>

        <div :class="{'block': showingNavigationDropdown, 'hidden': ! showingNavigationDropdown}" class="h-screen lg:hidden w-full flex justify-center flex-col items-center"> 
 
            <Link :href="route('about')" class="w-full flex justify-center py-3 text-sm trackicng-wide">About Us</Link> 
            <Link :href="route('faqs')" class="w-full flex justify-center py-3 text-sm trackicng-wide">FAQs</Link> 
            <Link :href="route('contact')" class="w-full flex justify-center py-3 text-sm trackicng-wide">Contact Us</Link> 

            <Link :href="route('register')" class="w-full mt-5 border-2 border-primaryColor rounded-full flex justify-center py-2 text-sm trackicng-wide font-bold text-gray-600">Create account</Link> 

            <Link :href="route('login')" class="w-full border-2 border-primaryColor bg-primaryColor text-white rounded-full mt-3 flex justify-center py-2 text-sm trackicng-wide font-bold ">Login</Link> 
        </div>
    </header>
</template>

<script>
import { Link } from '@inertiajs/vue3';
import Icon from '@/Components/Global/Icon.vue';

export default {
    components :{
        Link,
        Icon
    },

    data(){
        return {
            showingNavigationDropdown: false, 

        }
    }
}
</script>