<template>
    <AppLayout title="Frequently Asked Questions And Answers">
        <div class="p-5 lg:p-16"> 

            <div class="w-full items-center justify-between">
                <h1 class="text-lg md:text-xl lg:text-3xl font-bold">Question & Answer</h1>
                <p class="text-sm md:text-md text-gray-500 mt-3 ">Control how your bot responds to questions and messages. Tailor your own custom responses.</p>
            </div>

            <div v-if="all_faqs.length" class="w-full mt-16 border border-gray-100 rounded-lg px-8">
                <div class="py-3 mt-6 mb-10">
                    <div class="w-full flex items-center justify-between">
                        <h1 class="text-md md:text-lg font-semibold text-gray-500">Saved FAQs</h1>

                        <div class="flex md:items-center flex-col md:flex-row space-y-2 md:space-y-0 md:space-x-3">
                            <button @click="deleteFaq()" v-if="!currentlySelectedFaq" class="text-white bg-red-400 rounded-md py-2 px-4 md:px-6 hover:opacity-90 text-sm md:text-md">Delete Selected </button>

                            <button @click="visible = true" class="flex items-center px-5 py-2 text-white hover:opacity-85 bg-primaryColor rounded-md text-sm md:text-md justify-center">
                                <icon name="plus" class="w-4 text-white"></icon>
                                Add 
                            </button>
                        </div>
                    </div>
                </div>
                
                <DataTable v-model:selection="form.selectedFaqs" :value="all_faqs" dataKey="id" scrollable class="w-[280px] md:w-full ">
                    <Column selectionMode="multiple"  headerStyle="width: 2rem"></Column>
                    <Column field="question" header="Question" style="min-width: 150px"></Column>
                    <Column field="answer" header="Answer" style="min-width: 250px"></Column>
                    
                </DataTable>
 
            </div>

            <div v-else class="w-full border border-gray-200 rounded-lg py-10 flex justify-center flex-col items-center mt-16">

                <span class="text-sm md:text-md text-gray-500">
                    No Data Added!
                </span>

                <button @click="visible = true" class="flex items-center px-5 py-2 text-white hover:opacity-85 bg-primaryColor rounded-md mt-6 text-sm md:text-md">
                    <icon name="plus" class="w-4 text-white"></icon>
                    Add
                </button>
            </div>
        </div>

        <Dialog v-model:visible="visible" modal header="Add FAQ" :style="{ width: '30rem' }"> 

            <div class="w-full flex flex-col items-start mb-4">
                <label for="username" class="font-semibold w-24">Question</label>
                <TextInput id="username" v-model="form.question" class="w-full flex-auto mt-2" autocomplete="off" />
            </div>

            <div class="w-full flex flex-col items-start mb-4">
                <label for="username" class="font-semibold w-24">Answer</label> 
                <textarea  v-model="form.answer" name="" id="" class="w-full rounded-md border border-gray-300 flex-auto mt-2" rows="4"></textarea>
            </div>

            <div class="flex justify-end gap-2">
                <Button type="button" label="Cancel" severity="secondary" @click="visible = false"></Button>

                <Button :disabled="form.question == '' && form.answer == ''" type="button"  label="Save" @click="submitFaq"></Button>
            </div>
        </Dialog>
    </AppLayout>

    <ConfirmDialog></ConfirmDialog>

</template>

<script> 
import AppLayout from '@/Layouts/AppLayout.vue'; 
import Icon from '@/Components/Global/Icon.vue'    
import {  Link } from '@inertiajs/vue3'; 
import DataTable from 'primevue/datatable';
import Column from 'primevue/column';    
import TextInput from '@/Components/TextInput.vue'; 
import LargeSpinner from '@/Components/Global/LargeSpinner.vue';
import PrimaryButton from '@/Components/PrimaryButton.vue';
import 'primeicons/primeicons.css' 
import Button from 'primevue/button'; 
import Dialog from 'primevue/dialog';
import ConfirmDialog from 'primevue/confirmdialog'; 
import 'primeicons/primeicons.css' 

export default {
    components :{ 
        AppLayout,
        Icon,
        Link, 
        DataTable,
        Column, 
        TextInput,
        LargeSpinner,
        PrimaryButton,
        Button, 
        Dialog,
        ConfirmDialog
    },

    props : {
        currently_selected_chatbot : Object ,
        faqs : Array ,
    },

    data() {
        return {  
            embed_type : 1 ,
            all_faqs: this.faqs,
            form : {
                selectedFaqs: null, 
                question: '', 
                answer: '', 
                chatbot_id : this.currently_selected_chatbot.id
            },

            loading : false, 
            visible : false, 
        }
    },

    methods: { 
            
            submitFaq(){
                this.$inertia.post(route('submit_faq'), this.form , { 
                    preserveState: false, 
                    
                });
            },
 
        deleteFaq() {
            this.$confirm.require({
                message: 'Do you want to delete selected faq (s)?',
                header: 'Delete FAQ',
                icon: 'pi pi-info-circle',
                rejectProps: {
                    label: 'Cancel',
                    severity: 'secondary',
                    outlined: true
                },
                acceptProps: {
                    label: 'Delete',
                    severity: 'danger'
                },

                accept: () => {
                    this.$inertia.post(route('delete_faqs'), this.form , { 
                        preserveState: false, 
                    });
                },
                reject: () => {
                    
                }
            });
        }

        },

    computed : {
        currentlySelectedFaq(){
            return this.form.selectedFaqs == null || this.form.selectedFaqs == '';
        }
    }
}
</script>