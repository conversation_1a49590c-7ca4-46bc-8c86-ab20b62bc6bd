<template>
      <Head title="Onboarding" />

    <div class="w-full lg:h-screen lg:overflow-hidden">
        <div class="w-full h-14 lg:h-[10%] flex flex-col justify-center px-5 lg:px-20 border-b border-gray-200">
    
            <div class="h-full w-full flex items-center space-x-2 justify-between">
                <Link href="/" class="flex items-center space-x-4">
                    <img src="/img/logos/logo.webp" class="h-10 lg:h-12" alt="">  
                    <div class="flex items-baseline">
                        <span class="text-xl font-bold">Kimana</span> 
                        <span class="text-4xl ml-[3px] font-extrabold text-primaryColor">.</span> 
                    </div>
                </Link>
     
                <div class="flex items-center space-x-3 lg:space-x-5">
                    <a href="mailto:<EMAIL>" class="px-3 lg:px-6 font-semibold hover:bg-gray-100 focus:bg-gray-100 py-2 rounded-full text-xs lg:text-sm text-gray-500 hidden md:flex border border-gray-500">
                        Free consultation
                    </a>
    
                    <div class="ms-3 relative">
                        <Dropdown align="right" width="48">
                            <template #trigger>
                                <button v-if="$page.props.jetstream.managesProfilePhotos" class="flex text-sm border-2 border-transparent rounded-full focus:outline-none focus:border-gray-300 transition">
                                    <img class="h-8 w-8 rounded-full object-cover" :src="$page.props.auth.user.profile_photo_url" :alt="$page.props.auth.user.name">
                                </button>
    
                                <span v-else class="inline-flex rounded-md">
                                    <button type="button" class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-gray-500 bg-white hover:text-gray-700 focus:outline-none focus:bg-gray-50 active:bg-gray-50 transition ease-in-out duration-150">
                                        {{ $page.props.auth.user.name }}
    
                                        <svg class="ms-2 -me-0.5 h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" d="M19.5 8.25l-7.5 7.5-7.5-7.5" />
                                        </svg>
                                    </button>
                                </span>
                            </template>
    
                            <template #content>
                                <!-- Account Management -->
                                <div class="block px-4 py-2 text-xs text-gray-400">
                                    Manage Account
                                </div>
    
                                <div class="border-t border-gray-200" />
    
                                <!-- Authentication -->
                                <form @submit.prevent="logout">
                                    <DropdownLink as="button">
                                        Log Out
                                    </DropdownLink>
                                </form>
                            </template>
                        </Dropdown>
                    </div>
                </div>
            </div>
        </div>
         
        <div class="w-full lg:h-[90%] overflow-y-auto ">
            <div v-if="auth_user.onboarding_step == 1" class="my-16 lg:my-28 w-full px-5 flex justify-center">
        
                <form @submit.prevent="changeStep(1, 'plus')"  class="w-full lg:w-3/5 rounded-2xl shadow-md border border-gray-100">
                    <div class="p-7 border-b border-gray-100">
                        <h1 class="text-lg md:text-xl lg:text-2xl font-extrabold">Welcome to Kimana &#x1F44B; </h1>
                    </div>
        
                    <div class="py-6 px-8">
                        <p class="text-sm md:text-md text-gray-600">Please take a moment to review and update the following details about your company in the form below :</p>
        
                        <div class="mt-6 flex flex-col lg:flex-row space-y-5 lg:space-y-0 items-center lg:space-x-8">
                            <div class="w-full lg:w-1/2 flex flex-col">
                                <h1 class="text-sm font-bold mb-1">Company name</h1>
        
                                <TextInput
                                    id="email"
                                    v-model="form.company_name"
                                    type="text"
                                    class="mt-1 block w-full"
                                    required
                                    autofocus
                                    autocomplete="company_name"
                                    />
                            </div>
        
                            <div class="w-full lg:w-1/2 flex flex-col">
                                <h1 class="text-sm font-bold mb-1">Industry</h1>
        
                                <Combobox v-model="form.industry">
                                    <div class="relative mt-1">
                                        <div
                                        class="relative w-full cursor-default overflow-hidden rounded-lg bg-white text-left border border-gray-300  focus:outline-none focus-visible:ring-2  py-0.5 focus-visible:ring-white/75 focus-visible:ring-offset-2 focus-visible:ring-offset-primaryColor text-md"
                                        >
                                        <ComboboxInput
                                            class="w-full border-none py-2 pl-3 pr-10 text-md leading-5 text-gray-900 focus:ring-0"
                                            :displayValue="(industry) => industry.name"
                                            @change="industry_query = $event.target.value"
                                        />
        
                                        <ComboboxButton
                                            class="absolute inset-y-0 right-0 flex items-center pr-2"
                                        >
                                            <icon name="chevron_up"  class="text-gray-600 w-4"></icon>
                                        </ComboboxButton>
                                        </div>
                                        
                                        <TransitionRoot
                                        leave="transition ease-in duration-100"
                                        leaveFrom="opacity-100"
                                        leaveTo="opacity-0"
                                        @after-leave="industry_query = ''"
                                        >
                                        <ComboboxOptions
                                            class="absolute mt-1 max-h-60 w-full overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black/5 focus:outline-none sm:text-sm"
                                        >
                                            <div
                                            v-if="filteredIndustries.length === 0 && industry_query !== ''"
                                            class="relative cursor-default select-none px-4 py-2 text-gray-700"
                                            >
                                            Nothing found.
                                            </div>
        
                                            <ComboboxOption
                                                v-for="industry in filteredIndustries"
                                                as="template"
                                                :key="industry.id"
                                                :value="industry"
                                                v-slot="{ selected, active }"
                                                >
                                                    <li
                                                        class="relative cursor-default select-none py-2 pl-10 pr-4"
                                                        :class="{
                                                        'bg-primaryColor text-white': active,
                                                        'text-gray-900': !active,
                                                        }"
                                                    >
                                                        <span
                                                        class="block truncate"
                                                        :class="{ 'font-medium': selected, 'font-normal': !selected }"
                                                        >
                                                        {{ industry.name }}
                                                        </span>
                                                        <span
                                                        v-if="selected"
                                                        class="absolute inset-y-0 left-0 flex items-center pl-3"
                                                        :class="{ 'text-white': active, 'text-teal-600': !active }"
                                                        > 
                                                        <icon name="check" class="w-4 text-gray-300"></icon>
                                                        </span>
                                                    </li>
                                            </ComboboxOption>
                                        </ComboboxOptions>
                                        </TransitionRoot>
                                    </div>
                                </Combobox>
                            </div>
                        </div>
        
                        <div class="mt-6 flex flex-col lg:flex-row space-y-5 lg:space-y-0 items-center lg:space-x-8">
                            <div class="w-full lg:w-1/2 flex flex-col">
                                <h1 class="text-sm font-bold mb-1">Company website</h1>
        
                                <TextInput
                                        id="email"
                                        v-model="form.company_website"
                                        type="text"
                                        class="mt-1 block w-full"
                                        required
                                        autofocus
                                        autocomplete="company_website"
                                    />
                            </div>
        
                            <div class="w-full lg:w-1/2 flex flex-col">
                                <h1 class="text-sm font-bold mb-1">Company size</h1>
        
                                <select v-model="form.company_size" class="w-full border border-gray-200 rounded-md text-md" name="" id="">
                                    <option value="1">It's Only Me</option>
                                    <option value="2 - 9">2 - 9 </option>
                                    <option value="10 - 19">10 - 20 </option>
                                    <option value="20 - 100">20 - 100 </option>
                                    <option value="100+">Morethan 100 </option>
                                </select>
                            </div>
                        </div>
        
                        <div class="mt-6">
                            <h1 class="text-sm font-bold mb-1">Audience</h1>
        
                            <div class="flex items-center space-x-4">
                                <div 
                                v-for="(audience, index) in audiences" 
                                :key="index"
                                class="flex items-center space-x-2">
                                    <input v-model="form.audience" class="rounded-md w-5 h-5" type="checkbox" name="audience" :value="audience.name">
        
                                    <span class="text-sm text-gray-500">{{audience.bold_name}}</span>
                                </div> 
                            </div>
                        </div>
        
                        <div class="mt-6 w-full flex justify-end">
                            
                            <PrimaryButton type="submit" class="bg-primaryCoor flex items-center mt-6">
                                Save
                                <icon name="chevron_right" class="w-4 ml-2"></icon>
                            </PrimaryButton>
                        </div>
                    </div>
                </form>
            </div>
        
            <div v-if="auth_user.onboarding_step == 2" class="my-28">
        
                <div class="w-full flex justify-center ">
                    <div class="w-full lg:w-3/5 px-6">
                        <button @click="changeStep(2, 'plus')" class="px-5 py-2 rounded-md bg-primaryColor text-white flex items-center">
                            Start building your chatbot
                            <icon name="plus" class="text-white w-4 ml-2"></icon>
                        </button>
                    </div>
                </div>
        
                <div class="w-full flex justify-center mt-12 px-5">
                    <div class="w-full lg:w-3/5 border border-gray-100 rounded-2xl shadow-md py-8 px-6 md:px-12">
                        <h1 class="text-lg md:text-xl lg:text-2xl font-extrabold">Contact Us</h1>
        
                        <p class="text-md text-gray-500 mt-5">Our dedicated support team is here to assist you. Our operating hours are Monday to Friday, from 08:00 to 17:00.</p>
        
                        <div class="w-full mt-12 flex flex-col space-y-5 lg:space-y-0 lg:flex-row items-center">
                            <a href="tel+:+maito:<EMAIL>" class="w-full lg:w-1/2 py-10 hover:bg-gray-50 rounded-xl flex justify-center">
                                <div class="flex flex-col items-center">
                                    <div class="w-16 h-16 bg-gray-900 flex flex-col items-center justify-center rounded-full flex-shrink-0">
                                        <icon name="email" class="w-6 text-white"></icon>
                                    </div>
                                </div>
                            </a>
                            <a href="tel+:+" class="w-full lg:w-1/2 py-10 hover:bg-gray-50 rounded-xl flex justify-center">
                                <div class="flex flex-col items-center">
                                    <div class="w-16 h-16 bg-gray-900 flex flex-col items-center justify-center rounded-full flex-shrink-0">
                                        <icon name="phone" class="w-6 text-white"></icon>
                                    </div>
                                </div>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
             
            <div v-if="auth_user.onboarding_step == 3" class="w-full lg:h-full flex flex-col lg:flex-row space-y-10 lg:space-y-0 items-center lg:overflow-hidden"> 
    
                <div class="w-full lg:w-1/2 lg:h-full py-8 px-3 lg:px-12 overflow-y-auto overflow-x-none">
                    <h1 class="text-xl font-bold">Create Chatbot</h1>

                    <p class="text-sm mt-6">To create your bot we are going to need some data (information related to your company) that we will train the bot on. <br><br> You can provide this information by submitting important links with content you want the bot to know. Or you can just upload a document that contains your company information!</p>

                    <div v-if="chatForm.data_transfer_type == null"class="mt-10 flex items-center space-x-3 lg:space-x-5">
                        <button @click="selectDataTransferType(1)" class="w-1/2 rounded-2xl border border-gray-300 hover:bg-gray-50 border-dashed py-8 md:py-10 flex flex-col items-center justify-center">
                            <div class="w-full flex flex-col items-center">
                                <div class="w-10 md:w-14 h-10 md:h-14 rounded-xl bg-black flex flex-col items-center justify-center">
                                    <icon name="globe" class="w-5 text-white "></icon>
                                </div>

                                <span class="text-xs md:text-sm text-gray-500 mt-2">Website</span>
                            </div>
                        </button>

                        <button @click="selectDataTransferType(2)" class="w-1/2 rounded-2xl border border-gray-300 hover:bg-gray-50 border-dashed py-8 md:py-10 flex flex-col items-center justify-center">
                            <div class="w-full flex flex-col items-center">
                                <div class="w-10 md:w-14 h-10 md:h-14 rounded-xl bg-black flex flex-col items-center justify-center">
                                    <icon name="documents" class="w-5 text-white "></icon>
                                </div>

                                <span class="text-xs md:text-sm text-gray-500 mt-2">Document</span>
                            </div>
                        </button>

                    </div>

                    <div v-if="chatForm.data_transfer_type == 1" class="mt-6 flex flex-col items-start">
                        <span class="text-sm">Your website</span>

                        <div class="w-full flex items-stretch space-x-2">
                            <TextInput
                                id="email"
                                v-model="chatForm.website"
                                type="text"
                                class="mt-1 block w-[80%] min-h-full"
                                required
                                autofocus
                                autocomplete="company_website"
                            />

                            <button @click="getUrlLinks" class="py-1 w-[20%] hover:opacity-90 min-h-full rounded-lg bg-primaryColor text-white text-sm">Fetch Links</button>

                        </div>

                        
                        <div v-if="loading" class="w-full flex justify-center mt-16" >
                            <LargeSpinner/>
                        </div>

                        <div v-if="links_error == true" class="w-full flex justify-center">
                            <div class="mt-10 border border-dashed border-red-300 py-10 w-3/5 flex justify-center items-center flex-col">
                                <p class="text-sm text-red-600">Error while fetching links.</p>
                            </div>
                        </div>

                        <div class="w-full mt-8 px-2 overflow-clip py-5" v-if="chatForm.fetchedLinks.length > 0">

                            <p class="text-sm text-gray-500 mb-5">We found the following links. You can delete links you don't want, or add more to give more knowledge to your bot!</p>

                            <ul class="w-full mt-5">
                                <li class="w-[95%] " v-for="(link, index) in chatForm.fetchedLinks" :key="index"> 
                                    <div class="w-full py-2 flex items-center justify-center">
                                        <div class="w-[93%] py-2 "> 
                                            <span class="text-sm break-words text-gray-500">{{ link }}</span>
                                        </div> 

                                        <button @click="removeLink(index)" class="w-[7%] h-9 hover:bg-gray-100 cursor-pointer rounded-full flex justify-center flex-col items-center">
                                            <icon name="x" class="w-5 text-red-500"></icon>
                                        </button>
                                    </div>
                                </li>
                            </ul>

                            <div v-if="addMoreLinks == true"class="mt-10 flex items-center space-x-2">
                                <TextInput
                                        id="link"
                                        v-model="new_link"
                                        type="text"
                                        class="mt-1 block w-full" 
                                        autofocus 
                                    />

                                <button @click="addNewLinkToList" class="w-20 bg-gray-400 text-white text-center py-2 rounded-sm">Add</button>
                            </div>

                            <div class="w-full flex justify-start mt-5">
                                <button @click="addMoreLinks = !addMoreLinks" v-if="addMoreLinks == false" class="border-2 border-primaryColor px-4 py-1 text-xs text-primaryColor rounded-lg">Add More</button>
                            </div>

                            <div class="w-full flex justify-end mt-5">
                                <button @click="submitTrainingLinks()" v-if="addMoreLinks == false" class="border-2 border-primaryColor px-8 py-2 text-md bg-primaryColor rounded-lg text-white">Save</button>
                            </div>
                        </div>

                        <div v-else class="mt-8">
                            <span v-if="!loading && chatForm.website.length" class="text-sm">No links found!</span>
                        </div>
                    </div>

                    <div v-if="chatForm.data_transfer_type == 2" class="w-full">
                       
                        <div class="card mt-10">
                            <Toast />

                            <FileUpload name="demo[]" url="/api/upload" @upload="onTemplatedUpload($event)" :multiple="true" accept=".pdf" :maxFileSize="5000000" @select="onSelectedFiles">
                                <template #header="{ chooseCallback, uploadCallback, clearCallback, files }">
                                    <div class="flex flex-wrap justify-between items-center flex-1 gap-4">
                                        <span class="text-sm text-gray-500">
                                            Upload PDF(s)
                                        </span>

                                        <div class="flex gap-2">
                                            <Button @click="chooseCallback()" rounded outlined severity="secondary">
                                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-file-earmark-pdf" viewBox="0 0 16 16">
                                                <path d="M14 14V4.5L9.5 0H4a2 2 0 0 0-2 2v12a2 2 0 0 0 2 2h8a2 2 0 0 0 2-2M9.5 3A1.5 1.5 0 0 0 11 4.5h2V14a1 1 0 0 1-1 1H4a1 1 0 0 1-1-1V2a1 1 0 0 1 1-1h5.5z"/>
                                                <path d="M4.603 14.087a.8.8 0 0 1-.438-.42c-.195-.388-.13-.776.08-1.102.198-.307.526-.568.897-.787a7.7 7.7 0 0 1 1.482-.645 20 20 0 0 0 1.062-2.227 7.3 7.3 0 0 1-.43-1.295c-.086-.4-.119-.796-.046-1.136.075-.354.274-.672.65-.823.192-.077.4-.12.602-.077a.7.7 0 0 1 .477.365c.088.164.12.356.127.538.007.188-.012.396-.047.614-.084.51-.27 1.134-.52 1.794a11 11 0 0 0 .98 1.686 5.8 5.8 0 0 1 1.334.05c.364.066.734.195.96.465.12.144.193.32.2.518.007.192-.047.382-.138.563a1.04 1.04 0 0 1-.354.416.86.86 0 0 1-.51.138c-.331-.014-.654-.196-.933-.417a5.7 5.7 0 0 1-.911-.95 11.7 11.7 0 0 0-1.997.406 11.3 11.3 0 0 1-1.02 1.51c-.292.35-.609.656-.927.787a.8.8 0 0 1-.58.029m1.379-1.901q-.25.115-.459.238c-.328.194-.541.383-.647.547-.094.145-.096.25-.04.361q.016.032.026.044l.035-.012c.137-.056.355-.235.635-.572a8 8 0 0 0 .45-.606m1.64-1.33a13 13 0 0 1 1.01-.193 12 12 0 0 1-.51-.858 21 21 0 0 1-.5 1.05zm2.446.45q.226.245.435.41c.24.19.407.253.498.256a.1.1 0 0 0 .07-.015.3.3 0 0 0 .094-.125.44.44 0 0 0 .059-.2.1.1 0 0 0-.026-.063c-.052-.062-.2-.152-.518-.209a4 4 0 0 0-.612-.053zM8.078 7.8a7 7 0 0 0 .2-.828q.046-.282.038-.465a.6.6 0 0 0-.032-.198.5.5 0 0 0-.145.04c-.087.035-.158.106-.196.283-.04.192-.03.469.046.822q.036.167.09.346z"/>
                                                </svg>
                                            </Button>

                                            <Button @click="clearCallback()" icon="pi pi-times" rounded outlined severity="danger" :disabled="!files || files.length === 0"></Button>
                                        </div>

                                        <ProgressBar :value="totalSizePercent" :showValue="false" class="md:w-20rem h-1 w-full md:ml-auto">
                                            <span class="whitespace-nowrap">{{ totalSize }}B / 1Mb</span>
                                        </ProgressBar>
                                    </div>
                                </template>

                                <template #content="{ files, uploadedFiles, removeUploadedFileCallback, removeFileCallback, messages }">
                                    <div class="flex flex-col gap-8 pt-4">
                                        <Message v-for="message of messages" :key="message" :class="{ 'mb-8': !files.length && !uploadedFiles.length}" severity="error">
                                            {{ message }}
                                        </Message>

                                        <div v-if="files.length > 0">
                                            <div class="flex flex-wrap gap-4">
                                                <div v-for="(file, index) of files" :key="file.name + file.type + file.size" class="p-8 rounded-border flex flex-col border border-surface items-center gap-4">
                                                    <div> 
                                                        <div v-if="file.type == 'application/pdf'" class="">
                                                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-filetype-pdf" viewBox="0 0 16 16">
                                                            <path fill-rule="evenodd" d="M14 4.5V14a2 2 0 0 1-2 2h-1v-1h1a1 1 0 0 0 1-1V4.5h-2A1.5 1.5 0 0 1 9.5 3V1H4a1 1 0 0 0-1 1v9H2V2a2 2 0 0 1 2-2h5.5zM1.6 11.85H0v3.999h.791v-1.342h.803q.43 0 .732-.173.305-.175.463-.474a1.4 1.4 0 0 0 .161-.677q0-.375-.158-.677a1.2 1.2 0 0 0-.46-.477q-.3-.18-.732-.179m.545 1.333a.8.8 0 0 1-.085.38.57.57 0 0 1-.238.241.8.8 0 0 1-.375.082H.788V12.48h.66q.327 0 .512.181.185.183.185.522m1.217-1.333v3.999h1.46q.602 0 .998-.237a1.45 1.45 0 0 0 .595-.689q.196-.45.196-1.084 0-.63-.196-1.075a1.43 1.43 0 0 0-.589-.68q-.396-.234-1.005-.234zm.791.645h.563q.371 0 .609.152a.9.9 0 0 1 .354.454q.118.302.118.753a2.3 2.3 0 0 1-.068.592 1.1 1.1 0 0 1-.196.422.8.8 0 0 1-.334.252 1.3 1.3 0 0 1-.483.082h-.563zm3.743 1.763v1.591h-.79V11.85h2.548v.653H7.896v1.117h1.606v.638z"/>
                                                            </svg>
                                                        </div>

                                                        <div v-else class="">
                                                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-file-earmark-word" viewBox="0 0 16 16">
                                                            <path d="M5.485 6.879a.5.5 0 1 0-.97.242l1.5 6a.5.5 0 0 0 .967.01L8 9.402l1.018 3.73a.5.5 0 0 0 .967-.01l1.5-6a.5.5 0 0 0-.97-.242l-1.036 4.144-.997-3.655a.5.5 0 0 0-.964 0l-.997 3.655L5.485 6.88z"/>
                                                            <path d="M14 14V4.5L9.5 0H4a2 2 0 0 0-2 2v12a2 2 0 0 0 2 2h8a2 2 0 0 0 2-2M9.5 3A1.5 1.5 0 0 0 11 4.5h2V14a1 1 0 0 1-1 1H4a1 1 0 0 1-1-1V2a1 1 0 0 1 1-1h5.5z"/>
                                                            </svg>
                                                        </div>
                                                        
                                                    </div>

                                                    <span class="font-semibold text-ellipsis max-w-60 whitespace-nowrap overflow-hidden">{{ file.name }}</span>

                                                    <div>{{ formatSize(file.size) }}</div>
                                                    <Badge value="Pending" severity="warn" />
                                                    <Button icon="pi pi-times" @click="onRemoveTemplatingFile(file, removeFileCallback, index)" outlined rounded severity="danger" />
                                                </div>
                                            </div>
                                        </div>

                                        <div v-if="uploadedFiles.length > 0">
                                            <h5>Completed</h5>
                                            
                                            <div class="flex flex-wrap gap-4">
                                                <div v-for="(file, index) of uploadedFiles" :key="file.name + file.type + file.size" class="p-8 rounded-border flex flex-col border border-surface items-center gap-4">
                                                    <div> 
                                                        <div v-if="file.type == 'application/pdf'" class="">
                                                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-filetype-pdf" viewBox="0 0 16 16">
                                                            <path fill-rule="evenodd" d="M14 4.5V14a2 2 0 0 1-2 2h-1v-1h1a1 1 0 0 0 1-1V4.5h-2A1.5 1.5 0 0 1 9.5 3V1H4a1 1 0 0 0-1 1v9H2V2a2 2 0 0 1 2-2h5.5zM1.6 11.85H0v3.999h.791v-1.342h.803q.43 0 .732-.173.305-.175.463-.474a1.4 1.4 0 0 0 .161-.677q0-.375-.158-.677a1.2 1.2 0 0 0-.46-.477q-.3-.18-.732-.179m.545 1.333a.8.8 0 0 1-.085.38.57.57 0 0 1-.238.241.8.8 0 0 1-.375.082H.788V12.48h.66q.327 0 .512.181.185.183.185.522m1.217-1.333v3.999h1.46q.602 0 .998-.237a1.45 1.45 0 0 0 .595-.689q.196-.45.196-1.084 0-.63-.196-1.075a1.43 1.43 0 0 0-.589-.68q-.396-.234-1.005-.234zm.791.645h.563q.371 0 .609.152a.9.9 0 0 1 .354.454q.118.302.118.753a2.3 2.3 0 0 1-.068.592 1.1 1.1 0 0 1-.196.422.8.8 0 0 1-.334.252 1.3 1.3 0 0 1-.483.082h-.563zm3.743 1.763v1.591h-.79V11.85h2.548v.653H7.896v1.117h1.606v.638z"/>
                                                            </svg>
                                                        </div>

                                                        <div v-else class="">
                                                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-file-earmark-word" viewBox="0 0 16 16">
                                                            <path d="M5.485 6.879a.5.5 0 1 0-.97.242l1.5 6a.5.5 0 0 0 .967.01L8 9.402l1.018 3.73a.5.5 0 0 0 .967-.01l1.5-6a.5.5 0 0 0-.97-.242l-1.036 4.144-.997-3.655a.5.5 0 0 0-.964 0l-.997 3.655L5.485 6.88z"/>
                                                            <path d="M14 14V4.5L9.5 0H4a2 2 0 0 0-2 2v12a2 2 0 0 0 2 2h8a2 2 0 0 0 2-2M9.5 3A1.5 1.5 0 0 0 11 4.5h2V14a1 1 0 0 1-1 1H4a1 1 0 0 1-1-1V2a1 1 0 0 1 1-1h5.5z"/>
                                                            </svg>
                                                        </div>
                                                        
                                                    </div>
                                                    <span class="font-semibold text-ellipsis max-w-60 whitespace-nowrap overflow-hidden">{{ file.name }}</span>
                                                    <div>{{ formatSize(file.size) }}</div>
                                                    <Badge value="Completed" class="mt-4" severity="success" />
                                                    <Button icon="pi pi-times" @click="removeUploadedFileCallback(index)" outlined rounded severity="danger" />
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </template>
                                <template #empty>
                                    <div class="flex items-center justify-center flex-col">
                                        <i   class="pi pi-cloud-upload !border-2 !rounded-full !p-8 !text-4xl !text-muted-color" />


                                        <p class="mt-6 mb-10 text-sm">Drag and drop files to here to upload.</p>
                                    </div>
                                </template>
                            </FileUpload>

                            <div  class="w-full mt-10 flex justify-end">
                                <PrimaryButton @click="submitDocuments()" class="">Submit Documents</PrimaryButton>
                            </div>
                        </div>
                        
                    </div>
                </div>
    
                <div class="w-full lg:w-1/2 lg:h-full min-h-[500px] bg-gray-100 p-5 overflow-y-auto">
                    <div class="w-full">
                        <h1 class="text-md font-bold">Preview</h1>
                    </div>
    
                    <div v-if="auth_user.onboarding_step == 3" class="w-full flex justify-center mt-8">
                        <div class="w-full" v-if="chatForm.data_transfer_type == 1">
                            <iframe v-if="previewVisible" class="w-full h-[460px]" :src="chatForm.website" frameborder="0"></iframe>
                        </div>
                    </div>

                    <div v-if="auth_user.onboarding_step == 4" class="w-full flex justify-center mt-12">
                        <div class="w-[290px] bg-white h-[430px] rounded-2xl">
    
                            <div class="w-full flex justify-between items-center rounded-t-3xl bg-primaryColor h-[15%] px-5">
                                <h1 class="text-md font-extrabold text-white">Kimana</h1>
    
                                <div class="rounded-full border border-white px-2 py-1 flex items-center space-x-2 text-white text-xs">
                                    <icon name="profile" class="text-white h-4"></icon>
                                    profile
                                </div>
                            </div>

                            <div class="w-full h-[85%] rounded-b-3xl  flex flex-col justify-between">

                                <div class="w-full h-[82%] px-4 py-6">
                                    <div class="w-full flex items-end space-x-3">
                                        <div class="flex-shrink-0 rounded-full bg-primaryColor w-8 h-8 p-1.5 flex justify-center flex-col items-center">
                                            <img src="/img/bot.png"  alt="">
                                        </div>
    
                                        <div class="bg-gray-200 rounded-3xl p-4">
                                            <p class="text-xs text-gray-800">Welcome &#x1F44B; {{ auth_user.name }} ! How can i help you today? </p>
                                        </div>
                                    </div>
    
                                    <div class="w-full flex justify-end my-3">
                                        <div class="bg-primaryColor rounded-3xl p-3">
                                            <p class="text-xs text-white">What's your name ? </p>
                                        </div>
                                    </div>

                                    <div class="w-full flex items-end space-x-3">
                                        <div class="flex-shrink-0 rounded-full bg-primaryColor w-8 h-8 p-1.5 flex justify-center flex-col items-center">
                                            <img src="/img/bot.png"  alt="">
                                        </div>
    
                                        <div class="bg-gray-200 rounded-3xl p-4">
                                            <p class="text-xs text-gray-800">Hi there! My name is steven. How can i help you today? </p>
                                        </div>
                                    </div>
                                </div>

                                <div class="w-full h-[18%] px-3 py-2 border-t border-gray-200 flex items-center">
                                    <div class="w-[85%] h-full rounded-lg border-2 border-primaryColor p-2 flex items-center ">
                                        <input type="text" class="w-[90%] border-none focus:ring-0 text-xs text-gray-700" placeholder="Type a message...">
                                        <button class="w-[12%] h-6 rounded-full hover:bg-primaryColor flex flex-col items-center justify-center group">
                                            <icon name="send" class="w-4 text-primaryColor group-hover:text-white"></icon>
                                        </button>
                                    </div>

                                    <button class="w-[15%] flex justify-center">
                                        <icon name="refresh" class="w-4 h-4 text-primaryColor"></icon>
                                    </button>
                                </div>
                            </div>
    
                        </div>
                    </div>
                </div>
            </div>

            <div v-if="auth_user.onboarding_step == 4" class="w-full h-full flex  flex-col lg:flex-row items-center lg:overflow-hidden"> 
                <div class="w-full lg:w-1/2 h-full py-8 px-5 lg:px-12 lg:overflow-y-auto overflow-x-none">
                    <h1 class="text-xl font-bold">Chatbot Customization</h1>

                    <div class="mt-8 flex flex-col">
                        <span class="text-sm font-semibold">Bot Name</span>

                        <div class="w-4/5 h-10 flex items-center border border-gray-300 rounded-md px-3 mt-2">
                            <icon name="business" class="w-5 text-gray-500 "></icon>
                            
                            <input v-model="chatForm.chatbot_title" type="text" class="w-[90%] h-[95%]  border-none focus:ring-0 ">
                        </div> 

                    </div>
                    <div class="mt-8 flex flex-col">
                        <span class="text-sm font-semibold">Primary Bot Color</span>

                        <Vue3ColorPicker class="mt-5 shadow-sm" v-model="chatForm.chatbot_colour" mode="solid" :showColorList="false"  :showEyeDrop="true" type="HEX"/>
                    </div>

                    <div class="mt-12 flex justify-start">
                        <PrimaryButton @click="customizeBot" class="bg-primaryColor">save</PrimaryButton>
                    </div>
 
                </div>

                <div class="w-full lg:w-1/2 h-full bg-gray-100 p-5 overflow-y-auto">
                    <div class="w-full">
                        <h1 class="text-md font-bold">Preview</h1>
                    </div> 

                    <div class="w-full flex justify-center mt-10">
                        <div class="w-[290px] bg-white h-[430px] rounded-2xl">
 
                            <div :style="{ backgroundColor: currentChatbotColour }" class="w-full flex justify-between items-center rounded-t-3xl h-[15%] px-5">
                                <h1 class="text-md font-extrabold text-white">{{ currentChatbotTitle }}</h1>

                                <div class="rounded-full border border-white px-2 py-1 flex items-center space-x-2 text-white text-xs">
                                    <icon name="profile" class="text-white h-4"></icon>
                                    profile
                                </div>
                            </div>

                            <div class="w-full h-[85%] rounded-b-3xl  flex flex-col justify-between">

                                <div class="w-full h-[82%] px-4 py-6">
                                    <div class="w-full flex items-end space-x-3">
                                        <div :style="{ backgroundColor: currentChatbotColour }"  class="flex-shrink-0 rounded-full w-8 h-8 p-1.5 flex justify-center flex-col items-center">
                                            <img src="/img/bot.png"  alt="">
                                        </div>

                                        <div class="bg-gray-200 rounded-3xl p-4">
                                            <p class="text-xs text-gray-800">Welcome &#x1F44B; {{ auth_user.name }} ! How can i help you today? </p>
                                        </div>
                                    </div>

                                    <div class="w-full flex justify-end my-3">
                                        <div :style="{ backgroundColor: currentChatbotColour }"  class=" rounded-3xl p-3">
                                            <p class="text-xs text-white">What's your name ? </p>
                                        </div>
                                    </div>

                                    <div class="w-full flex items-end space-x-3">
                                        <div :style="{ backgroundColor: currentChatbotColour }"  class="flex-shrink-0 rounded-full w-8 h-8 p-1.5 flex justify-center flex-col items-center">
                                            <img src="/img/bot.png"  alt="">
                                        </div>

                                        <div class="bg-gray-200 rounded-3xl p-4">
                                            <p class="text-xs text-gray-800">Hi there! My name is steven. How can i help you today? </p>
                                        </div>
                                    </div>
                                </div>

                                <div class="w-full h-[18%] px-3 py-2 border-t border-gray-200 flex items-center">
                                    <div :style="{ borderColor : currentChatbotColour }"  class="w-[85%] h-full rounded-lg border-2  p-2 flex items-center ">
                                        <input type="text" class="w-[90%] border-none focus:ring-0 text-xs text-gray-700" placeholder="Type a message...">
                                        <button 
                                        :style="{ backgroundColor: isHovered ? currentChatbotColour : '', color: isHovered ? 'white' : currentChatbotColour }"
                                        class="w-[12%] h-6 rounded-full hover:bg-primaryColor flex flex-col items-center justify-center group"
                                        @mouseenter="isHovered = true"
                                        @mouseleave="isHovered = false"
                                        >
                                            <icon :style="{ color: isHovered ? 'white' : currentChatbotColour }"   name="send" class="w-4 group-hover:text-white"></icon>
                                        </button>
                                    </div>

                                    <button class="w-[15%] flex justify-center">
                                        <icon :style="{ color: currentChatbotColour }" name="refresh" class="w-4 h-4 "></icon>
                                    </button>
                                </div>
                            </div>

                        </div>
                    </div>
                </div>
            </div>
        </div>

    </div>

</template>

<script>
import { Head, Link , router} from '@inertiajs/vue3';
import AppLayout from '@/Layouts/AppLayout.vue';
import Icon from '@/Components/Global/Icon.vue';
import InputLabel from '@/Components/InputLabel.vue';
import TextInput from '@/Components/TextInput.vue'; 
import Dropdown from '@/Components/Dropdown.vue';
import DropdownLink from '@/Components/DropdownLink.vue'; 
import PrimaryButton from '@/Components/PrimaryButton.vue';
import LargeSpinner from '@/Components/Global/LargeSpinner.vue'     
import FileUpload from 'primevue/fileupload';
import Toast from 'primevue/toast';
import ProgressBar from 'primevue/progressbar';
import Button from 'primevue/button';
import Message from 'primevue/message';
import 'primeicons/primeicons.css' 
import {Vue3ColorPicker} from '@cyhnkckali/vue3-color-picker';
import '@cyhnkckali/vue3-color-picker/dist/style.css'
import Badge from 'primevue/badge';
import OverlayBadge from 'primevue/overlaybadge';

import {
    Combobox,
    ComboboxInput,
    ComboboxButton,
    ComboboxOptions,
    ComboboxOption,
    TransitionRoot,
    TransitionChild,
    Dialog,  
} from '@headlessui/vue' 

export default {
    components : {
        AppLayout,
        Head, Link ,
        Icon, 
        InputLabel,TextInput, 

        Dropdown,DropdownLink,

        Combobox,
        ComboboxInput,
        ComboboxButton,
        ComboboxOptions,
        ComboboxOption,
        TransitionRoot,
        TransitionChild,
        Dialog, 
        PrimaryButton,
        LargeSpinner,

        FileUpload,
        Toast,
        ProgressBar,
        Button,
        Message, 
        Vue3ColorPicker,
        Badge,
        OverlayBadge
    },

    props : {
         
    },

    data(){
        return {
            auth_user : this.$page.props.auth.user, 
  
            form :{ 
                    company_name : '',
                    industry : '',
                    company_website : '',
                    company_size : '',
                    audience : [],
                } ,

            chatForm : {
                website :'' ,
                fetchedLinks : [], 
                data_transfer_type : null,
                files: [],
                chatbot_title : 'Kimana',
                chatbot_colour : '#a6cc3f',
            },
                
            industries: [
                { id: 1, name: 'Aviation' },
                { id: 2, name: 'Education' },
                { id: 3, name: 'Healthcare' },
                { id: 4, name: 'Retail' },
                { id: 5, name: 'Finance' },
                { id: 6, name: 'Real Estate' },
                { id: 7, name: 'Manufacturing' },
                { id: 8, name: 'Technology' },
                { id: 9, name: 'E-commerce' },
                { id: 10, name: 'Hospitality' },
                { id: 11, name: 'Food & Beverage' },
                { id: 12, name: 'Transportation' },
                { id: 13, name: 'Telecommunications' },
                { id: 14, name: 'Construction' },
                { id: 15, name: 'Agriculture' },
                { id: 16, name: 'Legal Services' },
                { id: 17, name: 'Automotive' },
                { id: 18, name: 'Entertainment' },
                { id: 19, name: 'Marketing & Advertising' },
                { id: 20, name: 'Insurance' },
                { id: 21, name: 'Nonprofit' },
                { id: 22, name: 'Public Sector' },
                { id: 23, name: 'Energy & Utilities' },
                { id: 24, name: 'Pharmaceuticals' },
                { id: 25, name: 'Media & Publishing' },
                { id: 26, name: 'Logistics & Supply Chain' },
                { id: 27, name: 'Consulting' },
                { id: 28, name: 'Arts & Culture' },
                { id: 29, name: 'Mining' },
                { id: 30, name: 'Human Resources' },
                { id: 31, name: 'Biotechnology' },
                { id: 32, name: 'Tourism' },
                { id: 33, name: 'Security Services' },
                { id: 34, name: 'Environmental Services' },
                { id: 35, name: 'Sports & Recreation' }
            ],
 
            industry_query : '',

            audiences : [
                {
                    id : 1 , 
                    name : 'b2b',
                    bold_name : 'B2B', 
                },
                {
                    id : 2 , 
                    name : 'b2c',
                    bold_name : 'B2C', 
                },
                {
                    id : 3 , 
                    name : 'hybrid',
                    bold_name : 'Hybrid', 
                },
                {
                    id : 4 , 
                    name : 'internal',
                    bold_name : 'Internal', 
                },
                
            ],

            loading : false,
            links_error : false,
            no_links_found : false,
            previewUrl : '',
            previewVisible: false,
            addMoreLinks: false,

            new_link : '',

            totalSize: 0,
            totalSizePercent: 0,

            isHovered: false,

             
        }
    },

    methods : {

        changeStep(step , operation){
            this.$inertia.post(route('change_onboarding_step',[ step , operation]), this.form , { 
                preserveState: false, 
            });
        },
        
        async getUrlLinks() {
            const url = '/get-links';
            this.loading = true;
            this.linksError = false;

            try {
                // Send URL to server
                const response = await axios.post(url,  this.chatForm );

                const uniqueArray = [...new Set(response.data.links)];

                this.loadPreview();
                

                // Populate fetchedLinks with the response data
                this.chatForm.fetchedLinks = uniqueArray;

                this.chatForm.fetchedLinks.push(this.chatForm.website);
 
            } catch (error) {
                console.error('Error fetching links:', error);
                this.linksError = true;
            } finally {
                this.loading = false;
            }
        },

        removeLink(index){
            this.chatForm.fetchedLinks.splice(index, 1);
        },

        selectDataTransferType(type_){
            this.chatForm.data_transfer_type = type_;
        },  

        loadPreview() { 
            try {
                const sanitizedUrl = new URL(this.chatForm.website).href;  
                this.previewUrl = sanitizedUrl;

                this.previewVisible = true;
            } catch (error) {
                alert("Please enter a valid URL.");
            }
        },

        addNewLinkToList(){

            this.chatForm.fetchedLinks.push(this.new_link);
            this.new_link = '';
            this.addMoreLinks = false;

        },

        customizeBot(){
            this.$inertia.post(route('customize_chatbot'), this.chatForm , { 
                preserveState: false, 
                
            });
        },

        logout(){
            router.post(route('logout'));
        },

        onRemoveTemplatingFile(file, removeFileCallback, index) {
            removeFileCallback(index);
            this.totalSize -= parseInt(this.formatSize(file.size));
            this.totalSizePercent = this.totalSize / 10;
        },

        onSelectedFiles(event) {
            this.chatForm.files = event.files;
            this.chatForm.files.forEach((file) => {
                this.totalSize += parseInt(this.formatSize(file.size));
            });
        },
        
        onTemplatedUpload() {
            this.$toast.add({ severity: 'info', summary: 'Success', detail: 'File Uploaded', life: 3000 });
        },

        formatSize(bytes) {
            const k = 1024;
            const dm = 3;
            const sizes = this.$primevue.config.locale.fileSizeTypes;

            if (bytes === 0) {
                return `0 ${sizes[0]}`;
            }

            const i = Math.floor(Math.log(bytes) / Math.log(k));
            const formattedSize = parseFloat((bytes / Math.pow(k, i)).toFixed(dm));

            return `${formattedSize} ${sizes[i]}`;
        },

        submitDocuments(){
            this.$inertia.post(route('submit_training_documents'), this.chatForm , { 
                preserveState: false, 
                
            });
        },

        submitTrainingLinks(){
            this.$inertia.post(route('submit_training_links'), this.chatForm , { 
                preserveState: false, 
                
            });
        },
        

    },

    computed : { 

        filteredIndustries() {
        return this.industry_query === ''
            ? this.industries
            : this.industries.filter(industry =>
                industry.name
                    .toLowerCase()
                    .replace(/\s+/g, '')
                    .includes(this.industry_query.toLowerCase().replace(/\s+/g, ''))
            );
        },

        currentChatbotColour(){

            var colour = this.chatForm.chatbot_colour;

            return colour; 
        },

        currentChatbotTitle(){
            return this.chatForm.chatbot_title;
        }
    }, 

    
}
</script>
