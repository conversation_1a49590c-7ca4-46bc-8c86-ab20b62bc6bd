(function () {
    const chatbotId = window.chatbotId || 'default'; // Fetch the chatbot ID from the global scope

    fetch(`https://app.tkcmarketing.co/api/chatbot/${chatbotId}`)
        .then(response => response.json())
        .then(chatbot => {
            if (!chatbot || chatbot.error || !chatbot.random_id) {
                console.error('Chatbot data is invalid or missing:', chatbot?.error || 'Unknown error');
                return;
            }

            // Create the chatbot widget container
            const widget = document.createElement('div');
            widget.id = `chatbot-${chatbot.random_id}`;
            widget.style.position = 'fixed';
            widget.style.bottom = '70px';
            widget.style.right = '30px';
            widget.style.width = '340px';
            widget.style.height = '450px'; 
            widget.style.borderRadius = '20px';
            widget.style.overflow = 'hidden';
            widget.style.zIndex = 10000;
            widget.style.boxShadow = '0 4px 8px rgba(0, 0, 0, 0.1)';
            widget.style.display = 'none'; // Start hidden

            widget.innerHTML = ` 
                <iframe src="https://app.tkcmarketing.co/api/chatbot-iframe/${chatbot.random_id}" style="width: 340px; height: 450px; border: none;"></iframe>
            `; 
            document.body.appendChild(widget);
            // Create the toggle button
            const button = document.createElement('button');
            const buttonIconUrl = chatbot.widget_button_icon 
                ? `https://app.tkcmarketing.co/storage/${chatbot.widget_button_icon}` 
                : 'https://app.tkcmarketing.co/bot.png'; // Default image if no icon is provided
            const buttonIcon = document.createElement('img');
            buttonIcon.src = buttonIconUrl;
            buttonIcon.alt = 'Chatbot Icon';
            buttonIcon.style.width = '35px';
            buttonIcon.style.height = '35px';

            button.style.position = 'fixed';
            button.style.bottom = '20px';
            button.style.right = '10px';
            button.style.zIndex = 10001;
            button.style.background = chatbot.chatbot_colour || '#4682B4';
            button.style.color = 'white';
            button.style.border = 'none';
            button.style.padding = '12px';
            button.style.borderRadius = '50%';
            button.style.cursor = 'pointer';
            button.style.display = 'flex';
            button.style.alignItems = 'center';
            button.style.justifyContent = 'center';

            button.appendChild(buttonIcon);

            // Add click event listener to toggle visibility
            button.addEventListener('click', () => {
                widget.style.display = widget.style.display === 'none' ? 'block' : 'none';
            });
            document.body.appendChild(button);

        })
        .catch(err => console.error('Failed to load chatbot:', err));
})();
